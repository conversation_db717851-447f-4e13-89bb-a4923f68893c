<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lifeboard - Plugin Management (M5)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            min-height: 100vh;
            position: relative;
        }

        /* Navigation Bar */
        .nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-link {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .nav-link:hover {
            background-color: #f0f0f0;
        }

        /* Icon styles */
        .nav-icons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            text-decoration: none;
        }

        .icon:hover {
            background-color: #f0f0f0;
        }

        /* Main content area */
        .main-content {
            margin-top: 60px;
            padding: 20px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Plugin Management Header */
        .plugin-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .plugin-title {
            font-size: 1.5em;
            font-weight: 600;
            margin: 0 0 10px 0;
            color: #333;
        }

        .plugin-subtitle {
            color: #666;
            margin: 0 0 20px 0;
        }

        /* Filter and Search Controls */
        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            gap: 5px;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.2s ease;
        }

        .filter-btn:hover {
            background: #f8f9fa;
        }

        .filter-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .search-input {
            flex: 1;
            max-width: 300px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .search-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        /* Plugin Grid */
        .plugin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        /* Plugin Card */
        .plugin-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .plugin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .plugin-card.disabled {
            opacity: 0.7;
        }

        .plugin-card.error {
            border-left: 4px solid #dc3545;
        }

        .plugin-header-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .plugin-info {
            flex: 1;
        }

        .plugin-name {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
            margin: 0 0 5px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .plugin-version {
            font-size: 0.8em;
            color: #666;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
        }

        .plugin-status {
            font-size: 1.2em;
        }

        .status-enabled { color: #28a745; }
        .status-disabled { color: #6c757d; }
        .status-error { color: #dc3545; }
        .status-loading { color: #ffc107; }

        .plugin-description {
            color: #666;
            font-size: 0.9em;
            line-height: 1.4;
            margin-bottom: 15px;
        }

        .plugin-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8em;
            color: #888;
            margin-bottom: 15px;
        }

        .plugin-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn:hover {
            background: #f8f9fa;
        }

        .btn-primary {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #28a745;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .toggle-switch input:disabled + .slider {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: #ddd;
        }

        .plugin-requirement {
            font-size: 12px;
            color: #f39c12;
            margin-top: 5px;
            font-weight: bold;
        }

        .plugin-requirement.hidden {
            display: none;
        }

        /* Statistics Panel */
        .stats-panel {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            text-align: center;
        }

        .stat-item {
            padding: 10px;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: 600;
            color: #333;
        }

        .stat-label {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .plugin-grid {
                grid-template-columns: 1fr;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                max-width: none;
            }
        }

    </style>
</head>
<body>
    <nav class="nav">
        <div class="nav-menu">
            <a href="index.html" class="nav-link">Feed</a>
        </div>
        <div class="nav-icons">
            <div class="icon">⚙️</div>
        </div>
    </nav>

    <div class="main-content">
        <!-- Plugin Management Header -->
        <div class="plugin-header">
            <h1 class="plugin-title">🔌 Plugin Management (M5)</h1>
            <p class="plugin-subtitle">Manage your Lifeboard plugins with enhanced settings storage and state management</p>

            <!-- Statistics Panel -->
            <div class="stats-panel">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="total-plugins">3</div>
                        <div class="stat-label">Total Plugins</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="enabled-plugins">2</div>
                        <div class="stat-label">Enabled</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="disabled-plugins">1</div>
                        <div class="stat-label">Disabled</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="error-plugins">0</div>
                        <div class="stat-label">Errors</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter and Search Controls -->
        <div class="controls">
            <div class="filter-group">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="enabled">Enabled</button>
                <button class="filter-btn" data-filter="disabled">Disabled</button>
                <button class="filter-btn" data-filter="error">Error</button>
            </div>
            <input type="text" class="search-input" placeholder="🔍 Search plugins..." id="plugin-search">
            <button class="btn btn-primary" onclick="testPluginAPI()">🧪 Test M5 API</button>
        </div>

        <!-- Plugin Grid -->
        <div class="plugin-grid" id="plugin-grid">
            <!-- Limitless Plugin Card -->
            <div class="plugin-card disabled" data-plugin="limitless" data-state="disabled">
                <div class="plugin-header-row">
                    <div class="plugin-info">
                        <div class="plugin-name">
                            🌟 Limitless
                            <span class="plugin-version">v0.1.0</span>
                        </div>
                    </div>
                    <div class="plugin-status status-disabled">○</div>
                </div>

                <div class="plugin-description">
                    Advanced AI integration plugin with enhanced M5 settings storage and state management capabilities.
                </div>

                <div class="plugin-meta">
                    <span>Last activity: Never</span>
                    <span>Permissions: workspace, network</span>
                </div>

                <div class="plugin-actions">
                    <label class="toggle-switch" title="API key required to enable plugin">
                        <input type="checkbox" disabled onchange="togglePlugin('limitless', this)">
                        <span class="slider"></span>
                    </label>
                    <button class="btn" onclick="showPluginSettings('limitless')">⚙️ Settings</button>
                    <button class="btn" onclick="reloadPlugin('limitless')">🔄 Reload</button>
                    <div class="plugin-requirement">⚠️ API key required</div>
                </div>
            </div>

            <!-- Analytics Plugin Card -->
            <div class="plugin-card" data-plugin="analytics" data-state="disabled">
                <div class="plugin-header-row">
                    <div class="plugin-info">
                        <div class="plugin-name">
                            📊 Analytics
                            <span class="plugin-version">v2.1.0</span>
                        </div>
                    </div>
                    <div class="plugin-status status-disabled">○</div>
                </div>

                <div class="plugin-description">
                    Personal data visualization and insights with M5 persistent settings for dashboard preferences.
                </div>

                <div class="plugin-meta">
                    <span>Last activity: 1 day ago</span>
                    <span>Permissions: workspace, filesystem</span>
                </div>

                <div class="plugin-actions">
                    <label class="toggle-switch">
                        <input type="checkbox" onchange="togglePlugin('analytics', this)">
                        <span class="slider"></span>
                    </label>
                    <button class="btn" onclick="showPluginSettings('analytics')">⚙️ Settings</button>
                    <button class="btn" onclick="reloadPlugin('analytics')">🔄 Reload</button>
                </div>
            </div>

            <!-- Weather Plugin Card (Error State) -->
            <div class="plugin-card error" data-plugin="weather" data-state="error">
                <div class="plugin-header-row">
                    <div class="plugin-info">
                        <div class="plugin-name">
                            🌤️ Weather
                            <span class="plugin-version">v1.0.0</span>
                        </div>
                    </div>
                    <div class="plugin-status status-error">!</div>
                </div>

                <div class="plugin-description">
                    <strong>Error:</strong> API key not configured. Please update settings to enable this plugin.
                </div>

                <div class="plugin-meta">
                    <span>Failed to load</span>
                    <span>Permissions: network</span>
                </div>

                <div class="plugin-actions">
                    <label class="toggle-switch">
                        <input type="checkbox" disabled>
                        <span class="slider"></span>
                    </label>
                    <button class="btn btn-primary" onclick="showPluginSettings('weather')">🔧 Fix Settings</button>
                    <button class="btn" onclick="reloadPlugin('weather')">🔄 Retry</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal (Hidden by default) -->
    <div id="settings-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 2000; display: none;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; width: 500px; max-width: 90vw; max-height: 80vh; overflow-y: auto;">
            <h3 id="modal-title">Plugin Settings</h3>
            <div id="modal-content">
                <!-- Settings content will be populated by JavaScript -->
            </div>
            <div style="margin-top: 20px; text-align: right;">
                <button class="btn" onclick="closeSettingsModal()">Cancel</button>
                <button class="btn btn-primary" onclick="savePluginSettings()">Save Settings</button>
            </div>
        </div>
    </div>

    <script src="js/logger.js"></script>
    <script>
        // M5 Plugin Management JavaScript
        let currentPlugin = null;

        // Initialize page logging
        logger.logComponentLifecycle('plugins-page', 'init');
        logger.info('Plugins page initialized', {
            type: 'page_init',
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
        let pluginStates = {
            limitless: {
                enabled: false,
                canEnable: false,
                error: 'API key required',
                settings: { theme: 'dark', notifications: true, apiKey: '' }
            },
            analytics: { enabled: false, settings: { dashboardLayout: 'grid', refreshInterval: 300 } },
            weather: { enabled: false, error: 'API key not configured', settings: { apiKey: '', location: 'auto' } }
        };

        // Filter functionality
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // Update active filter
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                const filter = btn.dataset.filter;
                logger.logUserAction('filter_change', btn, { filter: filter });
                filterPlugins(filter);
            });
        });

        // Search functionality
        document.getElementById('plugin-search').addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();
            searchPlugins(query);
        });

        function filterPlugins(filter) {
            const cards = document.querySelectorAll('.plugin-card');
            cards.forEach(card => {
                const state = card.dataset.state;
                if (filter === 'all' || state === filter) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function searchPlugins(query) {
            const cards = document.querySelectorAll('.plugin-card');
            cards.forEach(card => {
                const text = card.textContent.toLowerCase();
                if (text.includes(query)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function togglePlugin(pluginId, checkbox) {
            const enabled = checkbox.checked;

            // Special handling for limitless plugin - check API key requirement
            if (pluginId === 'limitless' && enabled) {
                const pluginState = pluginStates[pluginId];
                if (!pluginState.canEnable) {
                    // Prevent enabling and show error
                    checkbox.checked = false;
                    showNotification('❌ API key required to enable Limitless plugin. Please configure settings first.');
                    return;
                }
            }

            pluginStates[pluginId].enabled = enabled;

            // Update UI
            const card = document.querySelector(`[data-plugin="${pluginId}"]`);
            const status = card.querySelector('.plugin-status');

            if (enabled) {
                card.dataset.state = 'enabled';
                status.className = 'plugin-status status-enabled';
                status.textContent = '●';
                card.classList.remove('disabled');
            } else {
                card.dataset.state = 'disabled';
                status.className = 'plugin-status status-disabled';
                status.textContent = '○';
                card.classList.add('disabled');
            }

            updateStats();

            // Simulate M5 API call
            console.log(`M5 API: ${enabled ? 'Enabling' : 'Disabling'} plugin:`, pluginId);
            showNotification(`Plugin ${pluginId} ${enabled ? 'enabled' : 'disabled'}`);
        }

        function showPluginSettings(pluginId) {
            currentPlugin = pluginId;
            const plugin = pluginStates[pluginId];

            document.getElementById('modal-title').textContent = `${pluginId.charAt(0).toUpperCase() + pluginId.slice(1)} Settings`;

            let content = '<div style="margin-bottom: 15px;">';

            if (pluginId === 'limitless') {
                content += `
                    <label style="display: block; margin-bottom: 10px;">
                        <strong>Theme:</strong><br>
                        <select style="width: 100%; padding: 5px; margin-top: 5px;">
                            <option value="dark" ${plugin.settings.theme === 'dark' ? 'selected' : ''}>Dark</option>
                            <option value="light" ${plugin.settings.theme === 'light' ? 'selected' : ''}>Light</option>
                            <option value="auto" ${plugin.settings.theme === 'auto' ? 'selected' : ''}>Auto</option>
                        </select>
                    </label>
                    <label style="display: block; margin-bottom: 10px;">
                        <input type="checkbox" ${plugin.settings.notifications ? 'checked' : ''}> Enable notifications
                    </label>
                    <label style="display: block; margin-bottom: 10px;">
                        <strong>API Key:</strong><br>
                        <input type="password" value="${plugin.settings.apiKey}" placeholder="Enter Limitless API key" style="width: 100%; padding: 5px; margin-top: 5px;">
                    </label>
                `;
            } else if (pluginId === 'analytics') {
                content += `
                    <label style="display: block; margin-bottom: 10px;">
                        <strong>Dashboard Layout:</strong><br>
                        <select style="width: 100%; padding: 5px; margin-top: 5px;">
                            <option value="grid" ${plugin.settings.dashboardLayout === 'grid' ? 'selected' : ''}>Grid</option>
                            <option value="list" ${plugin.settings.dashboardLayout === 'list' ? 'selected' : ''}>List</option>
                        </select>
                    </label>
                    <label style="display: block; margin-bottom: 10px;">
                        <strong>Refresh Interval (seconds):</strong><br>
                        <input type="number" value="${plugin.settings.refreshInterval}" min="60" max="3600" style="width: 100%; padding: 5px; margin-top: 5px;">
                    </label>
                `;
            } else if (pluginId === 'weather') {
                content += `
                    <div style="background: #fff3cd; padding: 10px; border-radius: 4px; margin-bottom: 15px; border: 1px solid #ffeaa7;">
                        <strong>⚠️ Configuration Required</strong><br>
                        This plugin needs an API key to function properly.
                    </div>
                    <label style="display: block; margin-bottom: 10px;">
                        <strong>Weather API Key:</strong><br>
                        <input type="password" value="${plugin.settings.apiKey}" placeholder="Enter OpenWeatherMap API key" style="width: 100%; padding: 5px; margin-top: 5px;">
                    </label>
                    <label style="display: block; margin-bottom: 10px;">
                        <strong>Location:</strong><br>
                        <input type="text" value="${plugin.settings.location}" placeholder="auto" style="width: 100%; padding: 5px; margin-top: 5px;">
                    </label>
                `;
            }

            content += '</div>';

            document.getElementById('modal-content').innerHTML = content;
            document.getElementById('settings-modal').style.display = 'block';

            console.log('M5 API: Opened settings modal for plugin:', pluginId);
        }

        function closeSettingsModal() {
            document.getElementById('settings-modal').style.display = 'none';
            currentPlugin = null;
        }

        function savePluginSettings() {
            if (currentPlugin) {
                // In a real implementation, this would save to the M5 settings storage
                console.log('M5 API: Saving settings for plugin:', currentPlugin);
                showNotification(`Settings saved for ${currentPlugin}`);
                closeSettingsModal();
            }
        }

        function reloadPlugin(pluginId) {
            console.log('M5 API: Reloading plugin:', pluginId);
            showNotification(`Reloading ${pluginId}...`);

            // Simulate reload
            setTimeout(() => {
                showNotification(`${pluginId} reloaded successfully`);
            }, 1000);
        }



        function updateStats() {
            const enabled = Object.values(pluginStates).filter(p => p.enabled).length;
            const disabled = Object.values(pluginStates).filter(p => !p.enabled && !p.error).length;
            const errors = Object.values(pluginStates).filter(p => p.error).length;

            document.getElementById('enabled-plugins').textContent = enabled;
            document.getElementById('disabled-plugins').textContent = disabled;
            document.getElementById('error-plugins').textContent = errors;
        }

        function updatePluginEnablementStatus(pluginId, canEnable, hasValidApiKey = false) {
            const pluginState = pluginStates[pluginId];
            const card = document.querySelector(`[data-plugin="${pluginId}"]`);
            const toggle = card.querySelector('.toggle-switch input');
            const requirement = card.querySelector('.plugin-requirement');

            pluginState.canEnable = canEnable;

            if (canEnable) {
                // Plugin can be enabled - remove restrictions
                toggle.disabled = false;
                toggle.title = '';
                if (requirement) {
                    requirement.classList.add('hidden');
                }

                // Clear error state
                if (pluginState.error === 'API key required') {
                    delete pluginState.error;
                }

                if (hasValidApiKey) {
                    showNotification('✅ API key validated! You can now enable the Limitless plugin.');
                }
            } else {
                // Plugin cannot be enabled - add restrictions
                toggle.disabled = true;
                toggle.checked = false;
                toggle.title = 'API key required to enable plugin';
                if (requirement) {
                    requirement.classList.remove('hidden');
                }

                // Ensure plugin is disabled
                pluginState.enabled = false;
                card.dataset.state = 'disabled';
                const status = card.querySelector('.plugin-status');
                status.className = 'plugin-status status-disabled';
                status.textContent = '○';
                card.classList.add('disabled');
            }

            updateStats();
        }

        function showNotification(message) {
            // Simple notification system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #007bff;
                color: white;
                padding: 15px 20px;
                border-radius: 4px;
                z-index: 3000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Test M5 API functionality
        function testPluginAPI() {
            console.log('🧪 Testing M5 Plugin API...');

            if (window.lifeboard) {
                // Test getting plugin states
                window.lifeboard.plugins.getStates().then(states => {
                    console.log('M5 Plugin States:', states);
                }).catch(err => {
                    console.log('Simulated M5 Plugin States (Demo Mode):', pluginStates);
                });

                // Test settings API
                window.lifeboard.settings.load('limitless').then(settings => {
                    console.log('M5 Limitless Settings:', settings);
                }).catch(err => {
                    console.log('Simulated M5 Settings (Demo Mode):', pluginStates.limitless.settings);
                });
            } else {
                console.log('Running in demo mode - M5 APIs simulated');
                showNotification('🧪 M5 API Demo - Check console for details');
            }
        }

        // Check if limitless plugin has valid API key on load
        function checkLimitlessApiKeyOnLoad() {
            const limitlessState = pluginStates.limitless;
            if (limitlessState.settings && limitlessState.settings.apiKey && limitlessState.settings.apiKey.trim() !== '') {
                // API key exists, assume it's valid (in real implementation, you'd validate it)
                // For now, we'll assume any non-empty API key is valid
                updatePluginEnablementStatus('limitless', true, false);
            }
        }

        // Initialize
        updateStats();
        checkLimitlessApiKeyOnLoad();
    </script>

</body>
</html>
