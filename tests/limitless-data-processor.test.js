/**
 * Tests for Limitless Data Processor
 *
 * Tests database operations, data validation, and UPSERT functionality
 */

const DataProcessor = require('../desktop/plugins/limitless/src/data-processor');

describe('DataProcessor', () => {
  let dataProcessor;
  let mockAPI;
  let mockLogger;
  let mockDB;

  beforeEach(() => {
    // Mock logger
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    };

    // Mock query builder chain
    const mockQueryBuilder = {
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      first: jest.fn().mockResolvedValue(null),
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      del: jest.fn().mockResolvedValue(0)
    };

    // Mock database function that returns query builder
    mockDB = {
      raw: jest.fn(),
      transaction: jest.fn(),
      select: jest.fn().mockReturnValue(mockQueryBuilder),
      where: jest.fn().mockReturnValue(mockQueryBuilder),
      orderBy: jest.fn().mockReturnValue(mockQueryBuilder),
      limit: jest.fn().mockReturnValue(mockQueryBuilder),
      offset: jest.fn().mockReturnValue(mockQueryBuilder),
      first: jest.fn().mockResolvedValue(null),
      del: jest.fn().mockReturnValue(mockQueryBuilder)
    };

    // Mock API with database as function
    mockAPI = {
      database: jest.fn().mockReturnValue(mockDB),
      user: {
        id: 'test-user-id'
      },
      settings: {
        get: jest.fn(),
        set: jest.fn()
      }
    };

    // Add raw and transaction methods to database function
    mockAPI.database.raw = jest.fn();
    mockAPI.database.transaction = jest.fn();

    dataProcessor = new DataProcessor(mockAPI, mockLogger);
  });

  describe('constructor', () => {
    test('should initialize with API and logger', () => {
      expect(dataProcessor.api).toBe(mockAPI);
      expect(dataProcessor.logger).toBe(mockLogger);
      expect(dataProcessor.tableName).toBe('limitless_lifelogs');
    });
  });

  describe('processLifelogs', () => {
    let mockTransaction;

    beforeEach(() => {
      mockTransaction = {
        raw: jest.fn(),
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDB.raw.mockResolvedValue({ rows: [{ exists: true }] });
      mockDB.transaction.mockResolvedValue(mockTransaction);
      mockAPI.database.raw.mockResolvedValue({ rows: [{ exists: true }] });
      mockAPI.database.transaction.mockResolvedValue(mockTransaction);
    });

    test('should process empty lifelog array', async () => {
      const result = await dataProcessor.processLifelogs([]);

      expect(result.success).toBe(true);
      expect(result.processed).toBe(0);
      expect(result.inserted).toBe(0);
      expect(result.updated).toBe(0);
    });

    test('should process single valid lifelog', async () => {
      const lifelogs = [{
        id: 'test-id',
        title: 'Test Lifelog',
        markdown: 'Test content',
        startTime: '2024-01-01T12:00:00Z',
        endTime: '2024-01-01T12:30:00Z'
      }];

      mockTransaction.raw.mockResolvedValue({ rows: [{ inserted: true }] });

      const result = await dataProcessor.processLifelogs(lifelogs);

      expect(result.success).toBe(true);
      expect(result.processed).toBe(1);
      expect(result.inserted).toBe(1);
      expect(result.updated).toBe(0);
    });

    test('should process multiple valid lifelogs', async () => {
      const lifelogs = [
        {
          id: 'test-id-1',
          title: 'Test Lifelog 1',
          markdown: 'Test content 1',
          startTime: '2024-01-01T12:00:00Z'
        },
        {
          id: 'test-id-2',
          title: 'Test Lifelog 2',
          markdown: 'Test content 2',
          startTime: '2024-01-01T13:00:00Z'
        }
      ];

      mockTransaction.raw
        .mockResolvedValueOnce({ rows: [{ inserted: true }] })
        .mockResolvedValueOnce({ rows: [{ inserted: false }] });

      const result = await dataProcessor.processLifelogs(lifelogs);

      expect(result.success).toBe(true);
      expect(result.processed).toBe(2);
      expect(result.inserted).toBe(1);
      expect(result.updated).toBe(1);
    });

    test('should skip invalid lifelogs', async () => {
      const lifelogs = [
        {
          id: 'valid-id',
          title: 'Valid Lifelog',
          markdown: 'Valid content',
          startTime: '2024-01-01T12:00:00Z'
        },
        {
          // Missing required id field
          title: 'Invalid Lifelog',
          markdown: 'Invalid content',
          startTime: '2024-01-01T13:00:00Z'
        }
      ];

      mockTransaction.raw.mockResolvedValue({ rows: [{ inserted: true }] });

      const result = await dataProcessor.processLifelogs(lifelogs);

      expect(result.success).toBe(true);
      expect(result.processed).toBe(2);
      expect(result.inserted).toBe(1);
      expect(result.updated).toBe(0);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Invalid lifelog data, skipping',
        expect.objectContaining({ lifelogId: undefined })
      );
    });

    test('should handle database errors gracefully', async () => {
      const lifelogs = [{
        id: 'test-id',
        title: 'Test Lifelog',
        markdown: 'Test content',
        startTime: '2024-01-01T12:00:00Z'
      }];

      mockTransaction.raw.mockRejectedValue(new Error('Database error'));

      await expect(dataProcessor.processLifelogs(lifelogs)).rejects.toThrow('Database error');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to process lifelogs:',
        expect.any(Error)
      );
    });

    test('should handle partial failures', async () => {
      const lifelogs = [
        {
          id: 'valid-id',
          title: 'Valid Lifelog',
          content: 'Valid content',
          timestamp: '2024-01-01T12:00:00Z',
          type: 'text',
          source: 'manual'
        },
        {
          id: '', // Invalid - empty id
          title: 'Invalid Lifelog',
          content: 'Invalid content',
          timestamp: '2024-01-01T13:00:00Z',
          type: 'text',
          source: 'manual'
        }
      ];

      mockTransaction.raw.mockResolvedValue({ rows: [{ inserted: true }] });

      const result = await dataProcessor.processLifelogs(lifelogs);

      expect(result.success).toBe(true);
      expect(result.processed).toBe(1);
      expect(result.skipped).toBe(1);
      expect(result.errors).toBe(0);
    });

    test('should handle metadata correctly', async () => {
      const lifelogs = [{
        id: 'test-id',
        title: 'Test Lifelog',
        content: 'Test content',
        timestamp: '2024-01-01T12:00:00Z',
        type: 'text',
        source: 'manual',
        metadata: {
          location: 'New York',
          mood: 'happy',
          tags: ['work', 'productivity']
        }
      }];

      mockTransaction.raw.mockResolvedValue({ rows: [{ inserted: true }] });

      const result = await dataProcessor.processLifelogs(lifelogs);

      expect(result.success).toBe(true);
      expect(result.processed).toBe(1);
      expect(mockTransaction.raw).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO limitless_lifelogs'),
        expect.arrayContaining([
          expect.stringContaining('{"originalId":"test-id"')
        ])
      );
    });
  });

  describe('validateLifelogData', () => {
    test('should validate complete lifelog', () => {
      const lifelog = {
        id: 'test-id',
        title: 'Test Lifelog',
        markdown: 'Test content'
      };

      const result = dataProcessor.validateLifelogData(lifelog);

      expect(result).toBe(true);
    });

    test('should reject lifelog with missing id', () => {
      const lifelog = {
        title: 'Test Lifelog',
        markdown: 'Test content'
      };

      const result = dataProcessor.validateLifelogData(lifelog);

      expect(result).toBe(false);
    });

    test('should reject lifelog with non-string id', () => {
      const lifelog = {
        id: 123,
        title: 'Test Lifelog',
        markdown: 'Test content'
      };

      const result = dataProcessor.validateLifelogData(lifelog);

      expect(result).toBe(false);
    });

    test('should reject lifelog with no content', () => {
      const lifelog = {
        id: 'test-id'
        // No title, markdown, or contents
      };

      const result = dataProcessor.validateLifelogData(lifelog);

      expect(result).toBe(false);
    });

    test('should accept lifelog with only title', () => {
      const lifelog = {
        id: 'test-id',
        title: 'Test Lifelog'
      };

      const result = dataProcessor.validateLifelogData(lifelog);

      expect(result).toBe(true);
    });

    test('should accept lifelog with only markdown', () => {
      const lifelog = {
        id: 'test-id',
        markdown: 'Test content'
      };

      const result = dataProcessor.validateLifelogData(lifelog);

      expect(result).toBe(true);
    });

    test('should accept lifelog with only contents', () => {
      const lifelog = {
        id: 'test-id',
        contents: ['item1', 'item2']
      };

      const result = dataProcessor.validateLifelogData(lifelog);

      expect(result).toBe(true);
    });

    test('should reject null/undefined lifelog', () => {
      expect(dataProcessor.validateLifelogData(null)).toBe(false);
      expect(dataProcessor.validateLifelogData(undefined)).toBe(false);
      expect(dataProcessor.validateLifelogData('not-an-object')).toBe(false);
    });
  });


  describe('getStoredLifelogs', () => {
    test('should return stored lifelogs', async () => {
      const mockLifelogs = [
        { id: 'lifelog1', title: 'Test 1', markdown: 'Content 1', contents: null, metadata: {} },
        { id: 'lifelog2', title: 'Test 2', markdown: 'Content 2', contents: null, metadata: {} }
      ];
      
      // Mock the query chain
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLifelogs)
      };
      
      mockAPI.database.mockReturnValue(mockQueryBuilder);
      
      const result = await dataProcessor.getStoredLifelogs();
      
      expect(result).toEqual(mockLifelogs);
      expect(mockAPI.database).toHaveBeenCalledWith('limitless_lifelogs');
    });

    test('should handle empty result', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue([])
      };
      
      mockAPI.database.mockReturnValue(mockQueryBuilder);
      
      const result = await dataProcessor.getStoredLifelogs();
      
      expect(result).toEqual([]);
    });

    test('should handle database error', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockRejectedValue(new Error('Database error'))
      };
      
      mockAPI.database.mockReturnValue(mockQueryBuilder);
      
      await expect(dataProcessor.getStoredLifelogs()).rejects.toThrow('Database error');
    });
  });

  describe('getStorageStats', () => {
    test('should return storage statistics', async () => {
      const mockStats = {
        total_records: 100,
        earliest_lifelog: '2024-01-01T00:00:00Z',
        latest_lifelog: '2024-12-31T23:59:59Z',
        last_updated: '2024-12-31T23:59:59Z'
      };
      
      // Mock the query chain
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockStats)
      };
      
      mockAPI.database.mockReturnValue(mockQueryBuilder);
      mockAPI.database.raw = jest.fn().mockReturnValue('COUNT(*) as total_records');
      
      const result = await dataProcessor.getStorageStats();
      
      expect(result).toEqual({
        totalRecords: 100,
        earliestLifelog: '2024-01-01T00:00:00Z',
        latestLifelog: '2024-12-31T23:59:59Z',
        lastUpdated: '2024-12-31T23:59:59Z'
      });
      expect(mockAPI.database).toHaveBeenCalledWith('limitless_lifelogs');
    });

    test('should handle empty database', async () => {
      const mockStats = {
        total_records: 0,
        earliest_lifelog: null,
        latest_lifelog: null,
        last_updated: null
      };
      
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockStats)
      };
      
      mockAPI.database.mockReturnValue(mockQueryBuilder);
      mockAPI.database.raw = jest.fn().mockReturnValue('COUNT(*) as total_records');
      
      const result = await dataProcessor.getStorageStats();
      
      expect(result.totalRecords).toBe(0);
      expect(result.earliestLifelog).toBeNull();
      expect(result.latestLifelog).toBeNull();
      expect(result.lastUpdated).toBeNull();
    });

    test('should handle database error', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        first: jest.fn().mockRejectedValue(new Error('Database error'))
      };
      
      mockAPI.database.mockReturnValue(mockQueryBuilder);
      
      await expect(dataProcessor.getStorageStats()).rejects.toThrow('Database error');
    });
  });

  describe('clearLifelogData', () => {
    test('should clear all lifelog data', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        del: jest.fn().mockResolvedValue(10)
      };
      
      mockAPI.database.mockReturnValue(mockQueryBuilder);
      
      const result = await dataProcessor.clearLifelogData();
      
      expect(result).toBe(10);
      expect(mockAPI.database).toHaveBeenCalledWith('limitless_lifelogs');
    });

    test('should handle no data to clear', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        del: jest.fn().mockResolvedValue(0)
      };
      
      mockAPI.database.mockReturnValue(mockQueryBuilder);
      
      const result = await dataProcessor.clearLifelogData();
      
      expect(result).toBe(0);
    });

    test('should handle database error', async () => {
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        del: jest.fn().mockRejectedValue(new Error('Database error'))
      };
      
      mockAPI.database.mockReturnValue(mockQueryBuilder);
      
      await expect(dataProcessor.clearLifelogData()).rejects.toThrow('Database error');
    });
  });
});