#!/bin/bash

# Module System Test Runner
# Runs comprehensive tests for the new module system functionality

set -e

echo "🧪 Running Module System Tests"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$TEST_DIR")"
NODE_MODULES="$PROJECT_ROOT/tests/node_modules"

# Ensure we're in the tests directory
cd "$TEST_DIR"

# Check if dependencies are installed
if [ ! -d "$NODE_MODULES" ]; then
    echo -e "${YELLOW}Installing test dependencies...${NC}"
    npm install
fi

# Function to run a test file
run_test() {
    local test_file="$1"
    local test_name="$2"
    
    echo -e "\n${BLUE}Running $test_name...${NC}"
    
    if npx jest "$test_file" --verbose --coverage --collectCoverageFrom="desktop/src/modules/**/*.js"; then
        echo -e "${GREEN}✅ $test_name passed${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name failed${NC}"
        return 1
    fi
}

# Function to check test file exists
check_test_file() {
    local test_file="$1"
    local test_name="$2"
    
    if [ ! -f "$test_file" ]; then
        echo -e "${RED}❌ Test file not found: $test_file${NC}"
        echo -e "${YELLOW}Skipping $test_name${NC}"
        return 1
    fi
    return 0
}

# Track test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test 1: BaseModule Tests
echo -e "\n${BLUE}=== BaseModule Tests ===${NC}"
if check_test_file "BaseModule.test.js" "BaseModule Tests"; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "BaseModule.test.js" "BaseModule Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
fi

# Test 2: ModuleManager Tests (when created)
echo -e "\n${BLUE}=== ModuleManager Tests ===${NC}"
if check_test_file "ModuleManager.test.js" "ModuleManager Tests"; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "ModuleManager.test.js" "ModuleManager Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
else
    echo -e "${YELLOW}ModuleManager tests not yet created${NC}"
fi

# Test 3: Module Integration Tests (when created)
echo -e "\n${BLUE}=== Module Integration Tests ===${NC}"
if check_test_file "ModuleIntegration.test.js" "Module Integration Tests"; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "ModuleIntegration.test.js" "Module Integration Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
else
    echo -e "${YELLOW}Module integration tests not yet created${NC}"
fi

# Test 4: Specific Module Tests
echo -e "\n${BLUE}=== Individual Module Tests ===${NC}"

# Limitless Module Tests
if check_test_file "modules/LimitlessModule.test.js" "Limitless Module Tests"; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "modules/LimitlessModule.test.js" "Limitless Module Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
else
    echo -e "${YELLOW}Limitless module tests not yet created${NC}"
fi

# Weather Module Tests
if check_test_file "modules/WeatherModule.test.js" "Weather Module Tests"; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "modules/WeatherModule.test.js" "Weather Module Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
else
    echo -e "${YELLOW}Weather module tests not yet created${NC}"
fi

# Billboard Module Tests
if check_test_file "modules/BillboardModule.test.js" "Billboard Module Tests"; then
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if run_test "modules/BillboardModule.test.js" "Billboard Module Tests"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
else
    echo -e "${YELLOW}Billboard module tests not yet created${NC}"
fi

# Test Summary
echo -e "\n${BLUE}=== Test Summary ===${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ] && [ $TOTAL_TESTS -gt 0 ]; then
    echo -e "\n${GREEN}🎉 All module tests passed!${NC}"
    exit 0
elif [ $TOTAL_TESTS -eq 0 ]; then
    echo -e "\n${YELLOW}⚠️  No tests were run${NC}"
    exit 1
else
    echo -e "\n${RED}💥 Some tests failed${NC}"
    exit 1
fi
