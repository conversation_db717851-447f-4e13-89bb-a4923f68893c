/**
 * Tests for Limitless Sync Manager
 *
 * Tests synchronization scheduling, execution, and error handling
 */

const SyncManager = require('../desktop/plugins/limitless/src/sync-manager');

describe('SyncManager', () => {
  let syncManager;
  let mockAPI;
  let mockLogger;
  let mockLimitlessAPI;
  let mockDataProcessor;

  beforeEach(() => {
    // Mock logger
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    };

    // Mock API
    mockAPI = {
      settings: {
        get: jest.fn(),
        set: jest.fn()
      },
      user: {
        id: 'test-user-id'
      }
    };

    // Mock Limitless API
    mockLimitlessAPI = {
      fetchLifelogs: jest.fn()
    };

    // Mock Data Processor
    mockDataProcessor = {
      processLifelogs: jest.fn()
    };

    syncManager = new SyncManager(mockAPI, mockLogger, mockLimitlessAPI, mockDataProcessor);
  });

  afterEach(() => {
    // Clean up timers
    if (syncManager.syncInterval) {
      clearInterval(syncManager.syncInterval);
    }
  });

  describe('constructor', () => {
    test('should initialize with correct defaults', () => {
      expect(syncManager.syncStatus).toBe('idle');
      expect(syncManager.isSyncing).toBe(false);
      expect(syncManager.lastSyncTime).toBe(null);
      expect(syncManager.nextSyncTime).toBe(null);
      expect(syncManager.defaultSyncInterval).toBe(4 * 60 * 60 * 1000);
    });
  });

  describe('initialize', () => {
    test('should initialize successfully when disabled', async () => {
      mockAPI.settings.get.mockResolvedValue({ enabled: false });

      await syncManager.initialize();

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Sync manager initialized',
        expect.objectContaining({ enabled: false })
      );
    });

    test('should start auto-sync when enabled', async () => {
      mockAPI.settings.get.mockResolvedValue({ 
        enabled: true, 
        apiKey: 'test-key',
        syncInterval: 2
      });

      await syncManager.initialize();

      expect(syncManager.syncInterval).not.toBe(null);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Auto-sync started with 2 hour interval'
      );
    });

    test('should handle initialization errors', async () => {
      mockAPI.settings.get.mockRejectedValue(new Error('Settings error'));

      await expect(syncManager.initialize()).rejects.toThrow('Settings error');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('startAutoSync', () => {
    test('should start auto-sync with default interval', async () => {
      mockAPI.settings.get.mockResolvedValue({ 
        enabled: true,
        apiKey: 'test-key'
      });

      await syncManager.startAutoSync();

      expect(syncManager.syncInterval).not.toBe(null);
      expect(syncManager.nextSyncTime).toBeInstanceOf(Date);
    });

    test('should start auto-sync with custom interval', async () => {
      mockAPI.settings.get.mockResolvedValue({ 
        enabled: true,
        apiKey: 'test-key',
        syncInterval: 8
      });

      await syncManager.startAutoSync();

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Auto-sync started with 8 hour interval'
      );
    });

    test('should clear existing interval before starting new one', async () => {
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      
      mockAPI.settings.get.mockResolvedValue({ 
        enabled: true,
        apiKey: 'test-key'
      });

      // Start twice
      await syncManager.startAutoSync();
      await syncManager.startAutoSync();

      expect(clearIntervalSpy).toHaveBeenCalled();
    });
  });

  describe('stopAutoSync', () => {
    test('should stop auto-sync', async () => {
      mockAPI.settings.get.mockResolvedValue({ 
        enabled: true,
        apiKey: 'test-key'
      });

      await syncManager.startAutoSync();
      await syncManager.stopAutoSync();

      expect(syncManager.syncInterval).toBe(null);
      expect(syncManager.nextSyncTime).toBe(null);
      expect(mockLogger.info).toHaveBeenCalledWith('Auto-sync stopped');
    });

    test('should handle stop when not running', async () => {
      await syncManager.stopAutoSync();

      expect(mockLogger.info).not.toHaveBeenCalled();
    });
  });

  describe('performSync', () => {
    beforeEach(() => {
      mockAPI.settings.get.mockResolvedValue({
        enabled: true,
        apiKey: 'test-key',
        syncInterval: 4
      });
    });

    test('should skip sync if already in progress', async () => {
      syncManager.isSyncing = true;

      const result = await syncManager.performSync();

      expect(result.success).toBe(false);
      expect(result.message).toBe('Sync already in progress');
    });

    test('should skip sync if plugin not enabled', async () => {
      mockAPI.settings.get.mockResolvedValue({ enabled: false });

      const result = await syncManager.performSync();

      expect(result.success).toBe(false);
      expect(result.message).toBe('Plugin not enabled');
    });

    test('should skip sync if API key not configured', async () => {
      mockAPI.settings.get.mockResolvedValue({ enabled: true });

      const result = await syncManager.performSync();

      expect(result.success).toBe(false);
      expect(result.message).toBe('API key not configured');
    });

    test('should perform successful sync', async () => {
      mockLimitlessAPI.fetchLifelogs.mockResolvedValue({
        success: true,
        data: [
          { id: '1', title: 'Test Lifelog 1' },
          { id: '2', title: 'Test Lifelog 2' }
        ],
        nextCursor: null
      });

      mockDataProcessor.processLifelogs.mockResolvedValue({
        success: true,
        processed: 2
      });

      const result = await syncManager.performSync();

      expect(result.success).toBe(true);
      expect(result.totalProcessed).toBe(2);
      expect(syncManager.syncStatus).toBe('idle');
      expect(syncManager.lastSyncTime).toBeTruthy();
    });

    test('should handle pagination with cursor', async () => {
      mockLimitlessAPI.fetchLifelogs
        .mockResolvedValueOnce({
          success: true,
          data: [{ id: '1', title: 'Test Lifelog 1' }],
          nextCursor: 'cursor-1'
        })
        .mockResolvedValueOnce({
          success: true,
          data: [{ id: '2', title: 'Test Lifelog 2' }],
          nextCursor: null
        });

      mockDataProcessor.processLifelogs.mockResolvedValue({
        success: true,
        processed: 1
      });

      const result = await syncManager.performSync();

      expect(result.success).toBe(true);
      expect(result.totalProcessed).toBe(2);
      expect(mockLimitlessAPI.fetchLifelogs).toHaveBeenCalledTimes(2);
    });

    test('should handle duplicate lifelogs within sync session', async () => {
      mockLimitlessAPI.fetchLifelogs.mockResolvedValue({
        success: true,
        data: [
          { id: '1', title: 'Test Lifelog 1' },
          { id: '1', title: 'Test Lifelog 1' }, // Duplicate
          { id: '2', title: 'Test Lifelog 2' }
        ],
        nextCursor: null
      });

      mockDataProcessor.processLifelogs.mockResolvedValue({
        success: true,
        processed: 2
      });

      const result = await syncManager.performSync();

      expect(result.success).toBe(true);
      expect(result.totalProcessed).toBe(2); // Should filter out duplicate
    });

    test('should handle API fetch errors', async () => {
      mockLimitlessAPI.fetchLifelogs.mockResolvedValue({
        success: false,
        error: 'API Error'
      });

      const result = await syncManager.performSync();

      expect(result.success).toBe(false);
      expect(result.error).toContain('API Error');
      expect(syncManager.syncStatus).toBe('error');
    });

    test('should handle data processor errors', async () => {
      mockLimitlessAPI.fetchLifelogs.mockResolvedValue({
        success: true,
        data: [{ id: '1', title: 'Test Lifelog' }],
        nextCursor: null
      });

      mockDataProcessor.processLifelogs.mockRejectedValue(new Error('Database error'));

      const result = await syncManager.performSync();

      expect(result.success).toBe(false);
      expect(result.error).toContain('Database error');
    });

    test('should respect cooldown period', async () => {
      // Set recent sync time
      syncManager.lastSyncTime = new Date(Date.now() - 60000).toISOString(); // 1 minute ago

      const result = await syncManager.performSync();

      expect(result.success).toBe(false);
      expect(result.message).toBe('Minimum sync interval not reached');
    });

    test('should ignore cooldown when forced', async () => {
      // Set recent sync time
      syncManager.lastSyncTime = new Date(Date.now() - 60000).toISOString(); // 1 minute ago

      mockLimitlessAPI.fetchLifelogs.mockResolvedValue({
        success: true,
        data: [],
        nextCursor: null
      });

      mockDataProcessor.processLifelogs.mockResolvedValue({
        success: true,
        processed: 0
      });

      const result = await syncManager.performSync({ force: true });

      expect(result.success).toBe(true);
    });

    test('should process lifelogs in batches', async () => {
      // Create 150 lifelogs to test batching
      const lifelogs = Array.from({ length: 150 }, (_, i) => ({
        id: `lifelog-${i}`,
        title: `Test Lifelog ${i}`
      }));

      mockLimitlessAPI.fetchLifelogs.mockResolvedValue({
        success: true,
        data: lifelogs,
        nextCursor: null
      });

      mockDataProcessor.processLifelogs.mockResolvedValue({
        success: true,
        processed: 100
      });

      const result = await syncManager.performSync();

      expect(result.success).toBe(true);
      expect(mockDataProcessor.processLifelogs).toHaveBeenCalledTimes(2); // 100 + 50
    });
  });

  describe('getSyncStatus', () => {
    test('should return current sync status', () => {
      syncManager.syncStatus = 'syncing';
      syncManager.isSyncing = true;
      syncManager.lastSyncTime = '2024-01-01T00:00:00Z';
      syncManager.nextSyncTime = new Date('2024-01-01T04:00:00Z');
      syncManager.syncError = 'Test error';

      const status = syncManager.getSyncStatus();

      expect(status).toEqual({
        status: 'syncing',
        isSyncing: true,
        lastSync: '2024-01-01T00:00:00Z',
        nextSync: new Date('2024-01-01T04:00:00Z'),
        error: 'Test error'
      });
    });
  });

  describe('getLastSyncTime', () => {
    test('should return last sync time as Date', () => {
      syncManager.lastSyncTime = '2024-01-01T00:00:00Z';

      const result = syncManager.getLastSyncTime();

      expect(result).toBeInstanceOf(Date);
      expect(result.toISOString()).toBe('2024-01-01T00:00:00.000Z');
    });

    test('should return null if no last sync time', () => {
      syncManager.lastSyncTime = null;

      const result = syncManager.getLastSyncTime();

      expect(result).toBe(null);
    });
  });

  describe('updateLastSyncTimestamp', () => {
    test('should update last sync timestamp in settings', async () => {
      mockAPI.settings.get.mockResolvedValue({ enabled: true });
      mockAPI.settings.set.mockResolvedValue();

      await syncManager.updateLastSyncTimestamp('2024-01-01T00:00:00Z');

      expect(mockAPI.settings.set).toHaveBeenCalledWith({
        enabled: true,
        lastSyncTime: '2024-01-01T00:00:00Z'
      });
    });

    test('should handle errors when updating timestamp', async () => {
      mockAPI.settings.get.mockRejectedValue(new Error('Settings error'));

      await syncManager.updateLastSyncTimestamp('2024-01-01T00:00:00Z');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to update last sync timestamp:',
        expect.any(Error)
      );
    });
  });

  describe('cleanup', () => {
    test('should clean up resources', async () => {
      mockAPI.settings.get.mockResolvedValue({ enabled: true, apiKey: 'test' });
      
      await syncManager.startAutoSync();
      await syncManager.cleanup();

      expect(syncManager.syncInterval).toBe(null);
      expect(syncManager.processedLifelogIds.size).toBe(0);
      expect(mockLogger.debug).toHaveBeenCalledWith('Sync manager cleaned up');
    });

    test('should handle cleanup errors', async () => {
      // Mock error during cleanup
      const originalClearInterval = global.clearInterval;
      global.clearInterval = jest.fn(() => {
        throw new Error('Cleanup error');
      });

      await syncManager.cleanup();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error during sync manager cleanup:',
        expect.any(Error)
      );

      // Restore original function
      global.clearInterval = originalClearInterval;
    });
  });
});