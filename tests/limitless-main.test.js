/**
 * Tests for Limitless Plugin Main Class
 *
 * Tests plugin initialization, configuration, commands, and lifecycle management
 */

const { LimitlessPlugin } = require('../desktop/plugins/limitless/main');

describe('LimitlessPlugin', () => {
  let plugin;
  let mockAPI;
  let mockLogger;
  let mockLimitlessAPI;
  let mockDataProcessor;
  let mockSyncManager;

  beforeEach(() => {
    // Mock logger
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    };

    // Mock API
    mockAPI = {
      logger: mockLogger,
      settings: {
        get: jest.fn(),
        set: jest.fn()
      },
      commands: {
        register: jest.fn()
      },
      modal: {
        open: jest.fn(),
        close: jest.fn()
      },
      notifications: {
        info: jest.fn(),
        success: jest.fn(),
        error: jest.fn()
      },
      user: {
        id: 'test-user-id'
      }
    };

    // Mock sync manager
    mockSyncManager = {
      initialize: jest.fn(),
      cleanup: jest.fn(),
      performSync: jest.fn(),
      getSyncStatus: jest.fn().mockReturnValue({
        status: 'idle',
        isSyncing: false,
        lastSync: null,
        nextSync: null,
        error: null
      })
    };

    // Mock limitless API
    mockLimitlessAPI = {
      validateAPIKey: jest.fn(),
      getApiStats: jest.fn().mockReturnValue({
        baseUrl: 'https://api.limitless.ai',
        rateLimitDelay: 1000,
        maxRetries: 3,
        retryDelay: 2000
      })
    };

    // Mock data processor
    mockDataProcessor = {
      getStorageStats: jest.fn().mockResolvedValue({
        totalLifelogs: 0,
        lastUpdated: null
      })
    };

    plugin = new LimitlessPlugin(mockAPI);
    
    // Replace with mocks
    plugin.syncManager = mockSyncManager;
    plugin.limitlessAPI = mockLimitlessAPI;
    plugin.dataProcessor = mockDataProcessor;
  });

  describe('constructor', () => {
    test('should initialize with API and create components', () => {
      expect(plugin.api).toBe(mockAPI);
      expect(plugin.logger).toBe(mockLogger);
      expect(plugin.isInitialized).toBe(false);
    });
  });

  describe('initialize', () => {
    test('should initialize successfully', async () => {
      await plugin.initialize();

      expect(mockSyncManager.initialize).toHaveBeenCalled();
      expect(mockAPI.commands.register).toHaveBeenCalledTimes(2);
      expect(plugin.isInitialized).toBe(true);
      expect(mockLogger.info).toHaveBeenCalledWith('Limitless plugin initialized successfully');
    });

    test('should handle initialization errors', async () => {
      mockSyncManager.initialize.mockRejectedValue(new Error('Sync manager error'));

      await expect(plugin.initialize()).rejects.toThrow('Sync manager error');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to initialize Limitless plugin',
        expect.any(Error)
      );
    });
  });

  describe('registerCommands', () => {
    test('should register configuration and sync commands', async () => {
      await plugin.registerCommands();

      expect(mockAPI.commands.register).toHaveBeenCalledWith('limitless-configure', {
        name: 'Configure Limitless Plugin',
        description: 'Configure Limitless AI API settings',
        callback: expect.any(Function)
      });

      expect(mockAPI.commands.register).toHaveBeenCalledWith('limitless-sync-now', {
        name: 'Sync Limitless Data Now',
        description: 'Manually trigger lifelog synchronization',
        callback: expect.any(Function)
      });
    });

    test('should handle missing commands API', async () => {
      plugin.api.commands = null;

      await plugin.registerCommands();

      expect(mockLogger.debug).not.toHaveBeenCalledWith('Limitless plugin commands registered');
    });

    test('should handle command registration errors', async () => {
      mockAPI.commands.register.mockImplementation(() => {
        throw new Error('Command registration failed');
      });

      await plugin.registerCommands();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to register commands',
        expect.any(Error)
      );
    });
  });

  describe('showConfigurationModal', () => {
    test('should show configuration modal with current settings', async () => {
      mockAPI.settings.get.mockResolvedValue({
        apiKey: 'test-key',
        enabled: true,
        syncInterval: 6
      });

      await plugin.showConfigurationModal();

      expect(mockAPI.modal.open).toHaveBeenCalledWith({
        title: 'Limitless Plugin Settings',
        content: expect.stringContaining('test-key'),
        width: '500px',
        buttons: expect.any(Array)
      });
    });

    test('should handle empty settings', async () => {
      mockAPI.settings.get.mockResolvedValue(null);

      await plugin.showConfigurationModal();

      expect(mockAPI.modal.open).toHaveBeenCalledWith(
        expect.objectContaining({
          content: expect.stringContaining('value=""')
        })
      );
    });

    test('should handle modal display errors', async () => {
      mockAPI.settings.get.mockRejectedValue(new Error('Settings error'));

      await plugin.showConfigurationModal();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to show configuration modal',
        expect.any(Error)
      );
    });
  });

  describe('saveSettings', () => {
    beforeEach(() => {
      // Mock DOM elements
      global.document = {
        getElementById: jest.fn().mockImplementation((id) => {
          const mockElements = {
            'limitless-api-key': { value: 'test-api-key' },
            'limitless-enabled': { checked: true },
            'limitless-sync-interval': { value: '4' }
          };
          return mockElements[id];
        })
      };
    });

    test('should save valid settings', async () => {
      await plugin.saveSettings();

      expect(mockAPI.settings.set).toHaveBeenCalledWith({
        apiKey: 'test-api-key',
        enabled: true,
        syncInterval: 4
      });

      expect(mockSyncManager.cleanup).toHaveBeenCalled();
      expect(mockSyncManager.initialize).toHaveBeenCalled();
      expect(mockAPI.notifications.success).toHaveBeenCalledWith('Settings saved successfully');
    });

    test('should reject when enabled but no API key', async () => {
      global.document.getElementById.mockImplementation((id) => {
        const mockElements = {
          'limitless-api-key': { value: '' },
          'limitless-enabled': { checked: true },
          'limitless-sync-interval': { value: '4' }
        };
        return mockElements[id];
      });

      await plugin.saveSettings();

      expect(mockAPI.notifications.error).toHaveBeenCalledWith(
        'API key is required when sync is enabled'
      );
      expect(mockAPI.settings.set).not.toHaveBeenCalled();
    });

    test('should reject invalid sync interval', async () => {
      global.document.getElementById.mockImplementation((id) => {
        const mockElements = {
          'limitless-api-key': { value: 'test-key' },
          'limitless-enabled': { checked: true },
          'limitless-sync-interval': { value: '25' }
        };
        return mockElements[id];
      });

      await plugin.saveSettings();

      expect(mockAPI.notifications.error).toHaveBeenCalledWith(
        'Sync interval must be between 1 and 24 hours'
      );
    });

    test('should handle settings save errors', async () => {
      mockAPI.settings.set.mockRejectedValue(new Error('Save error'));

      await plugin.saveSettings();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to save settings',
        expect.any(Error)
      );
      expect(mockAPI.notifications.error).toHaveBeenCalledWith(
        'Failed to save settings: Save error'
      );
    });
  });

  describe('performManualSync', () => {
    test('should perform successful manual sync', async () => {
      mockSyncManager.performSync.mockResolvedValue({
        success: true,
        totalProcessed: 10
      });

      await plugin.performManualSync();

      expect(mockSyncManager.performSync).toHaveBeenCalledWith({ force: true });
      expect(mockAPI.notifications.success).toHaveBeenCalledWith(
        'Sync completed! Processed 10 lifelogs'
      );
    });

    test('should handle sync failure', async () => {
      mockSyncManager.performSync.mockResolvedValue({
        success: false,
        message: 'API error'
      });

      await plugin.performManualSync();

      expect(mockAPI.notifications.error).toHaveBeenCalledWith('Sync failed: API error');
    });

    test('should handle sync errors', async () => {
      mockSyncManager.performSync.mockRejectedValue(new Error('Sync error'));

      await plugin.performManualSync();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Manual sync failed',
        expect.any(Error)
      );
      expect(mockAPI.notifications.error).toHaveBeenCalledWith(
        'Manual sync failed: Sync error'
      );
    });
  });

  describe('checkInitialSetup', () => {
    test('should log when plugin is configured', async () => {
      mockAPI.settings.get.mockResolvedValue({
        enabled: true,
        apiKey: 'test-key'
      });

      await plugin.checkInitialSetup();

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Limitless plugin is configured and enabled'
      );
    });

    test('should log when plugin is not configured', async () => {
      mockAPI.settings.get.mockResolvedValue({
        enabled: false
      });

      await plugin.checkInitialSetup();

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Limitless plugin not fully configured. Use the configure command to set up.'
      );
    });

    test('should handle setup check errors', async () => {
      mockAPI.settings.get.mockRejectedValue(new Error('Settings error'));

      await plugin.checkInitialSetup();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error during initial setup check',
        expect.any(Error)
      );
    });
  });

  describe('getStatus', () => {
    test('should return plugin status', () => {
      plugin.isInitialized = true;
      mockSyncManager.getSyncStatus.mockReturnValue({
        status: 'idle',
        isSyncing: false,
        lastSync: '2024-01-01T00:00:00Z',
        nextSync: new Date('2024-01-01T04:00:00Z'),
        error: null
      });

      const status = plugin.getStatus();

      expect(status).toEqual({
        initialized: true,
        enabled: true,
        lastSync: '2024-01-01T00:00:00Z',
        nextSync: new Date('2024-01-01T04:00:00Z'),
        syncStatus: 'idle',
        isSyncing: false,
        error: null
      });
    });

    test('should handle disabled status', () => {
      mockSyncManager.getSyncStatus.mockReturnValue({
        status: 'disabled',
        isSyncing: false,
        lastSync: null,
        nextSync: null,
        error: null
      });

      const status = plugin.getStatus();

      expect(status.enabled).toBe(false);
    });
  });

  describe('getStats', () => {
    test('should return plugin statistics', async () => {
      mockSyncManager.getSyncStatus.mockReturnValue({
        status: 'idle',
        isSyncing: false,
        lastSync: null,
        nextSync: null,
        error: null
      });

      const stats = await plugin.getStats();

      expect(stats).toEqual({
        sync: expect.objectContaining({
          status: 'idle',
          isSyncing: false
        }),
        storage: expect.objectContaining({
          totalLifelogs: 0
        }),
        api: expect.objectContaining({
          baseUrl: 'https://api.limitless.ai'
        })
      });
    });

    test('should handle storage stats errors', async () => {
      mockDataProcessor.getStorageStats.mockRejectedValue(new Error('Storage error'));

      const stats = await plugin.getStats();

      expect(stats.storage.error).toBe('Storage error');
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get plugin stats',
        expect.any(Error)
      );
    });
  });

  describe('cleanup', () => {
    test('should cleanup successfully', async () => {
      plugin.isInitialized = true;

      await plugin.cleanup();

      expect(mockSyncManager.cleanup).toHaveBeenCalled();
      expect(plugin.isInitialized).toBe(false);
      expect(mockLogger.info).toHaveBeenCalledWith('Limitless plugin cleanup completed');
    });

    test('should handle cleanup errors', async () => {
      mockSyncManager.cleanup.mockRejectedValue(new Error('Cleanup error'));

      await plugin.cleanup();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed during plugin cleanup',
        expect.any(Error)
      );
    });
  });

  describe('setupModalEventListeners', () => {
    let mockTestButton;
    let mockSaveButton;

    beforeEach(() => {
      // Create mock DOM elements
      mockTestButton = {
        addEventListener: jest.fn(),
        disabled: false,
        textContent: 'Test API Key'
      };

      mockSaveButton = {
        addEventListener: jest.fn()
      };

      // Mock DOM elements
      global.document = {
        getElementById: jest.fn().mockImplementation((id) => {
          const mockElements = {
            'limitless-test-api': mockTestButton,
            'limitless-save-settings': mockSaveButton,
            'limitless-api-key': {
              value: 'test-key'
            }
          };
          return mockElements[id];
        })
      };
    });

    test('should setup event listeners for modal buttons', () => {
      plugin.setupModalEventListeners();

      expect(mockTestButton.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
      expect(mockSaveButton.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
    });

    test('should handle missing modal elements', () => {
      global.document.getElementById.mockReturnValue(null);

      expect(() => plugin.setupModalEventListeners()).not.toThrow();
    });
  });

  describe('edge cases', () => {
    test('should handle null API parameter', () => {
      expect(() => new LimitlessPlugin(null)).toThrow();
    });

    test('should handle missing API methods gracefully', () => {
      const minimalAPI = {
        logger: mockLogger
      };

      expect(() => new LimitlessPlugin(minimalAPI)).not.toThrow();
    });

    test('should handle concurrent initialization attempts', async () => {
      const promise1 = plugin.initialize();
      const promise2 = plugin.initialize();

      await Promise.all([promise1, promise2]);

      expect(mockSyncManager.initialize).toHaveBeenCalledTimes(2);
    });
  });
});