const BaseService = require('../../backend/src/services/base-service');
const { Logger } = require('../../backend/src/utils/logger');

describe('BaseService', () => {
  let service;
  let mockLogger;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    };

    service = new BaseService('test-service', {
      logger: mockLogger,
      config: { enabled: true }
    });
  });

  afterEach(() => {
    if (service) {
      service.removeAllListeners();
    }
  });

  describe('constructor', () => {
    it('should create service with correct initial state', () => {
      expect(service.serviceName).toBe('test-service');
      expect(service.isInitialized).toBe(false);
      expect(service.isStarted).toBe(false);
      expect(service.status).toBe('stopped');
      expect(service.scheduledTasks).toBeInstanceOf(Map);
    });

    it('should use provided logger', () => {
      expect(service.logger).toBe(mockLogger);
    });

    it('should store configuration', () => {
      expect(service.config).toEqual({ enabled: true });
    });
  });

  describe('initialize', () => {
    it('should initialize service successfully', async () => {
      await service.initialize();
      
      expect(service.isInitialized).toBe(true);
      expect(service.status).toBe('initialized');
      expect(mockLogger.info).toHaveBeenCalledWith('Initializing test-service service');
    });

    it('should not initialize twice', async () => {
      await service.initialize();
      await service.initialize();
      
      expect(mockLogger.info).toHaveBeenCalledTimes(1);
    });

    it('should handle initialization errors', async () => {
      service.validateConfiguration = jest.fn().mockRejectedValue(new Error('Test error'));
      
      await expect(service.initialize()).rejects.toThrow('Test error');
      expect(service.isInitialized).toBe(false);
      expect(service.status).toBe('error');
    });
  });

  describe('start', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should start service successfully', async () => {
      await service.start();
      
      expect(service.isStarted).toBe(true);
      expect(service.status).toBe('running');
      expect(service.startTime).toBeInstanceOf(Date);
    });

    it('should not start if not initialized', async () => {
      const uninitializedService = new BaseService('test', { logger: mockLogger });
      
      await expect(uninitializedService.start()).rejects.toThrow('Cannot start test - service not initialized');
    });

    it('should not start twice', async () => {
      await service.start();
      await service.start();
      
      expect(mockLogger.info).toHaveBeenCalledTimes(2); // Once for initialize, once for start
    });
  });

  describe('stop', () => {
    beforeEach(async () => {
      await service.initialize();
      await service.start();
    });

    it('should stop service successfully', async () => {
      await service.stop();
      
      expect(service.isStarted).toBe(false);
      expect(service.status).toBe('stopped');
    });

    it('should not stop if not started', async () => {
      await service.stop();
      await service.stop();
      
      expect(mockLogger.info).toHaveBeenCalledTimes(3); // init, start, stop
    });
  });

  describe('cleanup', () => {
    it('should cleanup service successfully', async () => {
      await service.initialize();
      await service.start();
      await service.cleanup();
      
      expect(service.isInitialized).toBe(false);
      expect(service.status).toBe('cleaned');
    });

    it('should stop service if running during cleanup', async () => {
      await service.initialize();
      await service.start();
      
      const stopSpy = jest.spyOn(service, 'stop');
      await service.cleanup();
      
      expect(stopSpy).toHaveBeenCalled();
    });
  });

  describe('task scheduling', () => {
    beforeEach(async () => {
      await service.initialize();
      await service.start();
    });

    afterEach(async () => {
      await service.stop();
    });

    it('should schedule task successfully', () => {
      const taskFn = jest.fn();
      const taskId = service.scheduleTask('test-task', taskFn, 1000);
      
      expect(taskId).toBeDefined();
      expect(service.scheduledTasks.has(taskId)).toBe(true);
    });

    it('should execute scheduled task', (done) => {
      const taskFn = jest.fn(() => {
        expect(taskFn).toHaveBeenCalled();
        done();
      });
      
      service.scheduleTask('test-task', taskFn, 10);
    });

    it('should remove scheduled task', () => {
      const taskFn = jest.fn();
      const taskId = service.scheduleTask('test-task', taskFn, 1000);
      
      const removed = service.removeScheduledTask(taskId);
      
      expect(removed).toBe(true);
      expect(service.scheduledTasks.has(taskId)).toBe(false);
    });

    it('should handle task execution errors', (done) => {
      const taskFn = jest.fn(() => {
        throw new Error('Task error');
      });
      
      service.on('taskError', (data) => {
        expect(data.error).toBeInstanceOf(Error);
        expect(data.taskName).toBe('error-task');
        done();
      });
      
      service.scheduleTask('error-task', taskFn, 10);
    });
  });

  describe('configuration management', () => {
    it('should load default configuration', async () => {
      process.env.TEST_SERVICE_ENABLED = 'true';
      process.env.TEST_SERVICE_LOG_LEVEL = 'debug';
      
      await service.loadConfiguration();
      
      expect(service.config.enabled).toBe(true);
      expect(service.config.logLevel).toBe('debug');
      
      delete process.env.TEST_SERVICE_ENABLED;
      delete process.env.TEST_SERVICE_LOG_LEVEL;
    });

    it('should validate configuration', async () => {
      service.config.enabled = false;
      
      await expect(service.validateConfiguration()).rejects.toThrow('test-service service is disabled');
    });

    it('should return safe configuration', () => {
      service.config = {
        enabled: true,
        apiKey: 'secret-key',
        password: 'secret-password',
        normalValue: 'normal'
      };
      
      const safeConfig = service.getSafeConfig();
      
      expect(safeConfig.enabled).toBe(true);
      expect(safeConfig.apiKey).toBe('***');
      expect(safeConfig.password).toBe('***');
      expect(safeConfig.normalValue).toBe('normal');
    });
  });

  describe('status and stats', () => {
    it('should return correct status', () => {
      const status = service.getStatus();
      
      expect(status.serviceName).toBe('test-service');
      expect(status.status).toBe('stopped');
      expect(status.isInitialized).toBe(false);
      expect(status.isStarted).toBe(false);
      expect(status.stats).toBeDefined();
    });

    it('should update stats', () => {
      service.updateStats({ operationsCount: 5 });
      
      expect(service.stats.operationsCount).toBe(5);
      expect(service.lastActivity).toBeInstanceOf(Date);
    });

    it('should calculate uptime correctly', async () => {
      await service.initialize();
      await service.start();
      
      const status = service.getStatus();
      expect(status.uptime).toBeGreaterThan(0);
    });
  });

  describe('event handling', () => {
    it('should emit events during lifecycle', async () => {
      const initializedHandler = jest.fn();
      const startedHandler = jest.fn();
      const stoppedHandler = jest.fn();
      
      service.on('initialized', initializedHandler);
      service.on('started', startedHandler);
      service.on('stopped', stoppedHandler);
      
      await service.initialize();
      await service.start();
      await service.stop();
      
      expect(initializedHandler).toHaveBeenCalled();
      expect(startedHandler).toHaveBeenCalled();
      expect(stoppedHandler).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should handle errors during initialization', async () => {
      service.onInitialize = jest.fn().mockRejectedValue(new Error('Init error'));
      
      await expect(service.initialize()).rejects.toThrow('Init error');
      expect(service.status).toBe('error');
      expect(service.errors).toHaveLength(1);
      expect(service.errors[0].phase).toBe('initialization');
    });

    it('should handle errors during start', async () => {
      await service.initialize();
      service.onStart = jest.fn().mockRejectedValue(new Error('Start error'));
      
      await expect(service.start()).rejects.toThrow('Start error');
      expect(service.status).toBe('error');
    });

    it('should handle errors during stop', async () => {
      await service.initialize();
      await service.start();
      service.onStop = jest.fn().mockRejectedValue(new Error('Stop error'));
      
      await expect(service.stop()).rejects.toThrow('Stop error');
      expect(service.status).toBe('error');
    });
  });
});