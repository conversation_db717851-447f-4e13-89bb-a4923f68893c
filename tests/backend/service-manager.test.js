const ServiceManager = require('../../backend/src/services/service-manager');
const BaseService = require('../../backend/src/services/base-service');

class MockService extends BaseService {
  constructor(name, options = {}) {
    super(name, options);
    this.initializeCallCount = 0;
    this.startCallCount = 0;
    this.stopCallCount = 0;
    this.cleanupCallCount = 0;
  }

  async onInitialize() {
    this.initializeCallCount++;
    if (this.shouldFailInitialize) {
      throw new Error('Initialize failed');
    }
  }

  async onStart() {
    this.startCallCount++;
    if (this.shouldFailStart) {
      throw new Error('Start failed');
    }
  }

  async onStop() {
    this.stopCallCount++;
    if (this.shouldFailStop) {
      throw new Error('Stop failed');
    }
  }

  async onCleanup() {
    this.cleanupCallCount++;
    if (this.shouldFailCleanup) {
      throw new Error('Cleanup failed');
    }
  }
}

describe('ServiceManager', () => {
  let serviceManager;
  let mockLogger;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    };

    serviceManager = new ServiceManager({
      logger: mockLogger,
      logDir: '/tmp/test-logs'
    });
  });

  afterEach(async () => {
    if (serviceManager && serviceManager.isStarted) {
      await serviceManager.stop();
    }
    if (serviceManager && serviceManager.isInitialized) {
      await serviceManager.cleanup();
    }
  });

  describe('constructor', () => {
    it('should create service manager with correct initial state', () => {
      expect(serviceManager.services).toBeInstanceOf(Map);
      expect(serviceManager.serviceOrder).toEqual([]);
      expect(serviceManager.dependencies).toBeInstanceOf(Map);
      expect(serviceManager.isInitialized).toBe(false);
      expect(serviceManager.isStarted).toBe(false);
      expect(serviceManager.status).toBe('stopped');
    });

    it('should use provided logger', () => {
      expect(serviceManager.logger).toBe(mockLogger);
    });
  });

  describe('service registration', () => {
    it('should register service successfully', () => {
      const service = new MockService('test-service', { logger: mockLogger });
      
      serviceManager.registerService('test-service', service);
      
      expect(serviceManager.services.has('test-service')).toBe(true);
      expect(serviceManager.serviceOrder).toContain('test-service');
      expect(serviceManager.stats.totalServices).toBe(1);
    });

    it('should register service with dependencies', () => {
      const service1 = new MockService('service1', { logger: mockLogger });
      const service2 = new MockService('service2', { logger: mockLogger });
      
      serviceManager.registerService('service1', service1);
      serviceManager.registerService('service2', service2, {
        dependencies: ['service1']
      });
      
      expect(serviceManager.dependencies.get('service2')).toEqual(['service1']);
      expect(serviceManager.serviceOrder).toEqual(['service1', 'service2']);
    });

    it('should register service with priority', () => {
      const service1 = new MockService('low-priority', { logger: mockLogger });
      const service2 = new MockService('high-priority', { logger: mockLogger });
      
      serviceManager.registerService('low-priority', service1, { priority: 1 });
      serviceManager.registerService('high-priority', service2, { priority: 10 });
      
      expect(serviceManager.serviceOrder).toEqual(['high-priority', 'low-priority']);
    });

    it('should throw error when registering duplicate service', () => {
      const service = new MockService('test-service', { logger: mockLogger });
      
      serviceManager.registerService('test-service', service);
      
      expect(() => {
        serviceManager.registerService('test-service', service);
      }).toThrow('Service test-service is already registered');
    });

    it('should throw error for missing dependencies', () => {
      const service = new MockService('test-service', { logger: mockLogger });
      
      expect(() => {
        serviceManager.registerService('test-service', service, {
          dependencies: ['non-existent-service']
        });
      }).toThrow('Missing dependency: non-existent-service for service test-service');
    });

    it('should throw error for circular dependencies', () => {
      const service1 = new MockService('service1', { logger: mockLogger });
      const service2 = new MockService('service2', { logger: mockLogger });
      
      serviceManager.registerService('service1', service1, { dependencies: ['service2'] });
      
      expect(() => {
        serviceManager.registerService('service2', service2, { dependencies: ['service1'] });
      }).toThrow('Circular dependency detected involving service1');
    });
  });

  describe('service unregistration', () => {
    it('should unregister service successfully', async () => {
      const service = new MockService('test-service', { logger: mockLogger });
      serviceManager.registerService('test-service', service);
      
      const result = await serviceManager.unregisterService('test-service');
      
      expect(result).toBe(true);
      expect(serviceManager.services.has('test-service')).toBe(false);
      expect(serviceManager.serviceOrder).not.toContain('test-service');
    });

    it('should return false for non-existent service', async () => {
      const result = await serviceManager.unregisterService('non-existent');
      
      expect(result).toBe(false);
    });

    it('should cleanup running service during unregistration', async () => {
      const service = new MockService('test-service', { logger: mockLogger });
      serviceManager.registerService('test-service', service);
      
      await serviceManager.initialize();
      await serviceManager.start();
      
      const result = await serviceManager.unregisterService('test-service');
      
      expect(result).toBe(true);
      expect(service.stopCallCount).toBe(1);
      expect(service.cleanupCallCount).toBe(1);
    });
  });

  describe('service retrieval', () => {
    it('should get service by name', () => {
      const service = new MockService('test-service', { logger: mockLogger });
      serviceManager.registerService('test-service', service);
      
      const retrieved = serviceManager.getService('test-service');
      
      expect(retrieved).toBe(service);
    });

    it('should return null for non-existent service', () => {
      const retrieved = serviceManager.getService('non-existent');
      
      expect(retrieved).toBeNull();
    });

    it('should get all services', () => {
      const service1 = new MockService('service1', { logger: mockLogger });
      const service2 = new MockService('service2', { logger: mockLogger });
      
      serviceManager.registerService('service1', service1);
      serviceManager.registerService('service2', service2);
      
      const services = serviceManager.getServices();
      
      expect(services).toBeInstanceOf(Map);
      expect(services.size).toBe(2);
      expect(services.has('service1')).toBe(true);
      expect(services.has('service2')).toBe(true);
    });
  });

  describe('lifecycle management', () => {
    let service1, service2;

    beforeEach(() => {
      service1 = new MockService('service1', { logger: mockLogger });
      service2 = new MockService('service2', { logger: mockLogger });
      
      serviceManager.registerService('service1', service1);
      serviceManager.registerService('service2', service2, {
        dependencies: ['service1']
      });
    });

    it('should initialize services in correct order', async () => {
      await serviceManager.initialize();
      
      expect(serviceManager.isInitialized).toBe(true);
      expect(serviceManager.status).toBe('initialized');
      expect(service1.initializeCallCount).toBe(1);
      expect(service2.initializeCallCount).toBe(1);
    });

    it('should start services in correct order', async () => {
      await serviceManager.initialize();
      await serviceManager.start();
      
      expect(serviceManager.isStarted).toBe(true);
      expect(serviceManager.status).toBe('running');
      expect(service1.startCallCount).toBe(1);
      expect(service2.startCallCount).toBe(1);
    });

    it('should stop services in reverse order', async () => {
      await serviceManager.initialize();
      await serviceManager.start();
      await serviceManager.stop();
      
      expect(serviceManager.isStarted).toBe(false);
      expect(serviceManager.status).toBe('stopped');
      expect(service1.stopCallCount).toBe(1);
      expect(service2.stopCallCount).toBe(1);
    });

    it('should cleanup services in reverse order', async () => {
      await serviceManager.initialize();
      await serviceManager.start();
      await serviceManager.cleanup();
      
      expect(serviceManager.isInitialized).toBe(false);
      expect(serviceManager.status).toBe('cleaned');
      expect(service1.cleanupCallCount).toBe(1);
      expect(service2.cleanupCallCount).toBe(1);
    });

    it('should not start if not initialized', async () => {
      await expect(serviceManager.start()).rejects.toThrow('Service manager not initialized');
    });

    it('should not initialize twice', async () => {
      await serviceManager.initialize();
      await serviceManager.initialize();
      
      expect(service1.initializeCallCount).toBe(1);
      expect(service2.initializeCallCount).toBe(1);
    });
  });

  describe('error handling', () => {
    let service1, service2;

    beforeEach(() => {
      service1 = new MockService('service1', { logger: mockLogger });
      service2 = new MockService('service2', { logger: mockLogger });
      
      serviceManager.registerService('service1', service1);
      serviceManager.registerService('service2', service2);
    });

    it('should handle initialization errors', async () => {
      service1.shouldFailInitialize = true;
      
      await serviceManager.initialize();
      
      expect(serviceManager.isInitialized).toBe(true);
      expect(serviceManager.errors).toHaveLength(1);
      expect(serviceManager.errors[0].serviceName).toBe('service1');
      expect(serviceManager.errors[0].phase).toBe('initialization');
    });

    it('should handle start errors', async () => {
      service1.shouldFailStart = true;
      
      await serviceManager.initialize();
      await serviceManager.start();
      
      expect(serviceManager.isStarted).toBe(true);
      expect(serviceManager.errors).toHaveLength(1);
      expect(serviceManager.errors[0].serviceName).toBe('service1');
      expect(serviceManager.errors[0].phase).toBe('start');
    });

    it('should handle stop errors', async () => {
      await serviceManager.initialize();
      await serviceManager.start();
      
      service1.shouldFailStop = true;
      await serviceManager.stop();
      
      expect(serviceManager.isStarted).toBe(false);
      expect(serviceManager.errors).toHaveLength(1);
      expect(serviceManager.errors[0].serviceName).toBe('service1');
      expect(serviceManager.errors[0].phase).toBe('stop');
    });

    it('should emit service error events', async () => {
      const errorHandler = jest.fn();
      serviceManager.on('serviceError', errorHandler);
      
      await serviceManager.initialize();
      await serviceManager.start();
      
      // Simulate service error
      service1.emit('error', new Error('Service error'));
      
      expect(errorHandler).toHaveBeenCalledWith({
        serviceName: 'service1',
        error: expect.any(Error)
      });
    });
  });

  describe('health monitoring', () => {
    let service;

    beforeEach(() => {
      service = new MockService('test-service', { logger: mockLogger });
      serviceManager.registerService('test-service', service);
    });

    it('should start health monitoring when started', async () => {
      await serviceManager.initialize();
      await serviceManager.start();
      
      expect(serviceManager.healthCheckInterval).toBeTruthy();
    });

    it('should stop health monitoring when stopped', async () => {
      await serviceManager.initialize();
      await serviceManager.start();
      await serviceManager.stop();
      
      expect(serviceManager.healthCheckInterval).toBeNull();
    });

    it('should perform health check', async () => {
      await serviceManager.initialize();
      await serviceManager.start();
      
      const healthCheckHandler = jest.fn();
      serviceManager.on('healthCheck', healthCheckHandler);
      
      serviceManager.performHealthCheck();
      
      expect(healthCheckHandler).toHaveBeenCalled();
      expect(serviceManager.stats.lastHealthCheck).toBeInstanceOf(Date);
    });
  });

  describe('statistics', () => {
    it('should update statistics correctly', async () => {
      const service1 = new MockService('service1', { logger: mockLogger });
      const service2 = new MockService('service2', { logger: mockLogger });
      
      serviceManager.registerService('service1', service1);
      serviceManager.registerService('service2', service2);
      
      await serviceManager.initialize();
      await serviceManager.start();
      
      expect(serviceManager.stats.totalServices).toBe(2);
      expect(serviceManager.stats.runningServices).toBe(2);
      expect(serviceManager.stats.errorServices).toBe(0);
    });

    it('should track error services', async () => {
      const service = new MockService('test-service', { logger: mockLogger });
      serviceManager.registerService('test-service', service);
      
      await serviceManager.initialize();
      service.shouldFailStart = true;
      await serviceManager.start();
      
      serviceManager.updateStats();
      
      expect(serviceManager.stats.errorServices).toBe(1);
    });
  });

  describe('status reporting', () => {
    it('should return complete status', async () => {
      const service = new MockService('test-service', { logger: mockLogger });
      serviceManager.registerService('test-service', service);
      
      await serviceManager.initialize();
      await serviceManager.start();
      
      const status = serviceManager.getStatus();
      
      expect(status.status).toBe('running');
      expect(status.isInitialized).toBe(true);
      expect(status.isStarted).toBe(true);
      expect(status.startTime).toBeInstanceOf(Date);
      expect(status.uptime).toBeGreaterThan(0);
      expect(status.serviceOrder).toEqual(['test-service']);
      expect(status.stats).toBeDefined();
      expect(status.services).toBeDefined();
      expect(status.services['test-service']).toBeDefined();
    });

    it('should include service details in status', async () => {
      const service = new MockService('test-service', { logger: mockLogger });
      serviceManager.registerService('test-service', service, {
        priority: 5,
        dependencies: []
      });
      
      await serviceManager.initialize();
      
      const status = serviceManager.getStatus();
      
      expect(status.services['test-service'].priority).toBe(5);
      expect(status.services['test-service'].dependencies).toEqual([]);
      expect(status.services['test-service'].registeredAt).toBeInstanceOf(Date);
    });
  });

  describe('event handling', () => {
    it('should emit lifecycle events', async () => {
      const initializedHandler = jest.fn();
      const startedHandler = jest.fn();
      const stoppedHandler = jest.fn();
      const cleanupHandler = jest.fn();
      
      serviceManager.on('initialized', initializedHandler);
      serviceManager.on('started', startedHandler);
      serviceManager.on('stopped', stoppedHandler);
      serviceManager.on('cleanup', cleanupHandler);
      
      const service = new MockService('test-service', { logger: mockLogger });
      serviceManager.registerService('test-service', service);
      
      await serviceManager.initialize();
      await serviceManager.start();
      await serviceManager.stop();
      await serviceManager.cleanup();
      
      expect(initializedHandler).toHaveBeenCalled();
      expect(startedHandler).toHaveBeenCalled();
      expect(stoppedHandler).toHaveBeenCalled();
      expect(cleanupHandler).toHaveBeenCalled();
    });

    it('should emit service registration events', () => {
      const registeredHandler = jest.fn();
      const unregisteredHandler = jest.fn();
      
      serviceManager.on('serviceRegistered', registeredHandler);
      serviceManager.on('serviceUnregistered', unregisteredHandler);
      
      const service = new MockService('test-service', { logger: mockLogger });
      serviceManager.registerService('test-service', service);
      
      expect(registeredHandler).toHaveBeenCalledWith({
        serviceName: 'test-service',
        serviceInstance: service
      });
    });
  });
});