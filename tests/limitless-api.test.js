/**
 * Tests for Limitless API Integration
 *
 * Tests API key validation, lifelog fetching, and error handling
 */

const LimitlessAPI = require('../desktop/plugins/limitless/src/limitless-api');

describe('LimitlessAPI', () => {
  let limitlessAPI;
  let mockAPI;
  let mockLogger;

  beforeEach(() => {
    // Mock logger
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    };

    // Mock API
    mockAPI = {
      network: {
        fetch: jest.fn()
      }
    };

    limitlessAPI = new LimitlessAPI(mockAPI, mockLogger);
  });

  describe('constructor', () => {
    test('should initialize with correct defaults', () => {
      expect(limitlessAPI.baseUrl).toBe('https://api.limitless.ai');
      expect(limitlessAPI.rateLimitDelay).toBe(1000);
      expect(limitlessAPI.maxRetries).toBe(3);
      expect(limitlessAPI.retryDelay).toBe(2000);
    });
  });

  describe('validateAPIKey', () => {
    test('should return false for empty API key', async () => {
      const result = await limitlessAPI.validateAPIKey('');
      expect(result).toBe(false);
    });

    test('should return false for null API key', async () => {
      const result = await limitlessAPI.validateAPIKey(null);
      expect(result).toBe(false);
    });

    test('should return false for non-string API key', async () => {
      const result = await limitlessAPI.validateAPIKey(123);
      expect(result).toBe(false);
    });

    test('should return true for valid API key', async () => {
      // Mock successful response
      mockAPI.network.fetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map(),
        json: jest.fn().mockResolvedValue({ data: [] }),
        text: jest.fn().mockResolvedValue('')
      });

      const result = await limitlessAPI.validateAPIKey('valid-api-key');
      expect(result).toBe(true);
      expect(mockAPI.network.fetch).toHaveBeenCalledWith(
        'https://api.limitless.ai/v1/lifelogs?limit=1',
        {
          method: 'GET',
          headers: {
            'X-API-Key': 'valid-api-key',
            'Content-Type': 'application/json'
          }
        }
      );
    });

    test('should return false for invalid API key', async () => {
      // Mock failed response
      mockAPI.network.fetch.mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        headers: new Map(),
        json: jest.fn().mockResolvedValue({ error: 'Invalid API key' }),
        text: jest.fn().mockResolvedValue('Invalid API key')
      });

      const result = await limitlessAPI.validateAPIKey('invalid-api-key');
      expect(result).toBe(false);
    });

    test('should return false on network error', async () => {
      mockAPI.network.fetch.mockRejectedValue(new Error('Network error'));

      const result = await limitlessAPI.validateAPIKey('api-key');
      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('fetchLifelogs', () => {
    test('should fetch lifelogs successfully', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map(),
        json: jest.fn().mockResolvedValue({
          data: {
            lifelogs: [
              { id: '1', title: 'Test Lifelog', markdown: 'Test content' }
            ]
          },
          meta: {
            lifelogs: {
              count: 1,
              nextCursor: null
            }
          }
        }),
        text: jest.fn().mockResolvedValue('')
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.fetchLifelogs({
        apiKey: 'valid-api-key',
        limit: 10
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data[0].id).toBe('1');
      expect(result.nextCursor).toBe(null);
    });

    test('should handle pagination cursor', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map(),
        json: jest.fn().mockResolvedValue({
          data: {
            lifelogs: [
              { id: '1', title: 'Test Lifelog' }
            ]
          },
          meta: {
            lifelogs: {
              count: 1,
              nextCursor: 'next-cursor-token'
            }
          }
        }),
        text: jest.fn().mockResolvedValue('')
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.fetchLifelogs({
        apiKey: 'valid-api-key',
        cursor: 'current-cursor',
        limit: 10
      });

      expect(result.success).toBe(true);
      expect(result.nextCursor).toBe('next-cursor-token');
      expect(mockAPI.network.fetch).toHaveBeenCalledWith(
        expect.stringContaining('cursor=current-cursor'),
        expect.any(Object)
      );
    });

    test('should handle API errors', async () => {
      mockAPI.network.fetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        headers: new Map(),
        json: jest.fn().mockResolvedValue({ error: 'Server error' }),
        text: jest.fn().mockResolvedValue('Server error')
      });

      const result = await limitlessAPI.fetchLifelogs({
        apiKey: 'valid-api-key'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to fetch lifelogs');
      expect(result.data).toEqual([]);
    });

    test('should require API key', async () => {
      const result = await limitlessAPI.fetchLifelogs({});
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('API key is required');
    });

    test('should handle network errors with retry logic', async () => {
      mockAPI.network.fetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue({
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: new Map(),
          json: jest.fn().mockResolvedValue({
            data: { lifelogs: [] },
            meta: { lifelogs: { count: 0, nextCursor: null } }
          }),
          text: jest.fn().mockResolvedValue('')
        });

      const result = await limitlessAPI.fetchLifelogs({
        apiKey: 'valid-api-key'
      });

      expect(result.success).toBe(true);
      expect(mockAPI.network.fetch).toHaveBeenCalledTimes(3);
    });
  });

  describe('shouldRetry', () => {
    test('should return true for retryable errors', () => {
      const networkError = new Error('Network error occurred');
      const timeoutError = new Error('Request timeout');
      const rateLimitError = new Error('Rate limit exceeded');

      expect(limitlessAPI.shouldRetry(networkError)).toBe(true);
      expect(limitlessAPI.shouldRetry(timeoutError)).toBe(true);
      expect(limitlessAPI.shouldRetry(rateLimitError)).toBe(true);
    });

    test('should return false for non-retryable errors', () => {
      const authError = new Error('Authentication failed');
      const parseError = new Error('JSON parse error');

      expect(limitlessAPI.shouldRetry(authError)).toBe(false);
      expect(limitlessAPI.shouldRetry(parseError)).toBe(false);
    });
  });

  describe('getApiStats', () => {
    test('should return API statistics', () => {
      const stats = limitlessAPI.getApiStats();
      
      expect(stats).toEqual({
        baseUrl: 'https://api.limitless.ai',
        rateLimitDelay: 1000,
        maxRetries: 3,
        retryDelay: 2000
      });
    });
  });

  describe('sleep', () => {
    test('should resolve after specified time', async () => {
      const startTime = Date.now();
      await limitlessAPI.sleep(100);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeGreaterThanOrEqual(100);
    });
  });
});