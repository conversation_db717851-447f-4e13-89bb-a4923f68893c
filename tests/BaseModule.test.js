/**
 * BaseModule Unit Tests
 *
 * Comprehensive test suite for BaseModule functionality with focus on
 * centralized task management system.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

const BaseModule = require('../desktop/src/modules/BaseModule');

// Mock the CoreLogger to avoid file system operations during tests
jest.mock('../desktop/core/logger/CoreLogger', () => ({
  factory: jest.fn(() => ({
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    trace: jest.fn()
  }))
}));

// Test implementation of BaseModule for testing
class TestModule extends BaseModule {
  constructor(options = {}) {
    super({
      name: 'test-module',
      version: '1.0.0',
      description: 'Test module for unit testing',
      ...options
    });
    
    this.initializeCallCount = 0;
    this.startCallCount = 0;
    this.stopCallCount = 0;
  }

  async onInitialize() {
    this.initializeCallCount++;
    if (this.shouldFailInitialize) {
      throw new Error('Initialization failed');
    }
  }

  async onStart() {
    this.startCallCount++;
    if (this.shouldFailStart) {
      throw new Error('Start failed');
    }
  }

  async onStop() {
    this.stopCallCount++;
    if (this.shouldFailStop) {
      throw new Error('Stop failed');
    }
  }
}

describe('BaseModule', () => {
  let module;
  let mockLogger;

  beforeEach(() => {
    // Clear all timers before each test
    jest.clearAllTimers();
    jest.useFakeTimers();

    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      trace: jest.fn()
    };

    module = new TestModule({
      logger: mockLogger,
      config: { testSetting: 'value' },
      services: { database: 'mock-db' }
    });
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    test('should throw error when instantiated directly', () => {
      expect(() => new BaseModule()).toThrow('BaseModule is abstract and cannot be instantiated directly');
    });

    test('should throw error without required name', () => {
      expect(() => new TestModule({ name: null })).toThrow('Module name is required and must be a string');
    });

    test('should throw error without required version', () => {
      expect(() => new TestModule({ name: 'test', version: null })).toThrow('Module version is required and must be a string');
    });

    test('should initialize with correct properties', () => {
      expect(module.name).toBe('test-module');
      expect(module.version).toBe('1.0.0');
      expect(module.isInitialized).toBe(false);
      expect(module.isStarted).toBe(false);
      expect(module.isEnabled).toBe(true);
      expect(module.scheduledTasks).toBeInstanceOf(Map);
      expect(module.scheduledTasks.size).toBe(0);
    });
  });

  describe('Centralized Task Management', () => {
    describe('scheduleTask()', () => {
      test('should schedule a task successfully', () => {
        const taskFunction = jest.fn();
        const taskId = module.scheduleTask('test-task', taskFunction, 1000);

        expect(taskId).toBe('test-task');
        expect(module.scheduledTasks.has('test-task')).toBe(true);
        
        const task = module.scheduledTasks.get('test-task');
        expect(task.id).toBe('test-task');
        expect(task.function).toBe(taskFunction);
        expect(task.interval).toBe(1000);
        expect(task.runCount).toBe(0);
        expect(task.errorCount).toBe(0);
        expect(task.lastRun).toBeNull();
      });

      test('should throw error for duplicate task ID', () => {
        const taskFunction = jest.fn();
        module.scheduleTask('duplicate-task', taskFunction, 1000);

        expect(() => {
          module.scheduleTask('duplicate-task', taskFunction, 2000);
        }).toThrow("Task with ID 'duplicate-task' already exists");
      });

      test('should not start interval when module is not started', () => {
        const taskFunction = jest.fn();
        module.scheduleTask('test-task', taskFunction, 1000);

        const task = module.scheduledTasks.get('test-task');
        expect(task.intervalId).toBeNull();
      });

      test('should start interval when module is started', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn();
        module.scheduleTask('test-task', taskFunction, 1000);

        const task = module.scheduledTasks.get('test-task');
        expect(task.intervalId).not.toBeNull();
      });

      test('should execute scheduled task at correct intervals', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn();
        module.scheduleTask('test-task', taskFunction, 1000);

        // Fast-forward time to trigger task execution
        jest.advanceTimersByTime(1000);
        expect(taskFunction).toHaveBeenCalledTimes(1);

        jest.advanceTimersByTime(1000);
        expect(taskFunction).toHaveBeenCalledTimes(2);

        jest.advanceTimersByTime(500);
        expect(taskFunction).toHaveBeenCalledTimes(2);

        jest.advanceTimersByTime(500);
        expect(taskFunction).toHaveBeenCalledTimes(3);
      });

      test('should update task statistics on successful execution', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn().mockResolvedValue();
        module.scheduleTask('test-task', taskFunction, 1000);

        const task = module.scheduledTasks.get('test-task');
        expect(task.runCount).toBe(0);
        expect(task.errorCount).toBe(0);
        expect(task.lastRun).toBeNull();

        // Execute task
        jest.advanceTimersByTime(1000);
        await Promise.resolve(); // Allow async task to complete

        expect(task.runCount).toBe(1);
        expect(task.errorCount).toBe(0);
        expect(task.lastRun).toBeInstanceOf(Date);
      });

      test('should handle task execution errors gracefully', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn().mockRejectedValue(new Error('Task failed'));
        module.scheduleTask('test-task', taskFunction, 1000);

        const task = module.scheduledTasks.get('test-task');

        // Execute task
        jest.advanceTimersByTime(1000);
        await Promise.resolve(); // Allow async task to complete

        expect(task.runCount).toBe(0);
        expect(task.errorCount).toBe(1);
        expect(task.lastRun).toBeNull();
        expect(mockLogger.error).toHaveBeenCalledWith(
          'Scheduled task failed',
          expect.objectContaining({
            module: 'test-module',
            taskId: 'test-task',
            error: 'Task failed'
          })
        );
      });

      test('should support task options', () => {
        const taskFunction = jest.fn();
        const options = { description: 'Test task', priority: 'high' };
        
        module.scheduleTask('test-task', taskFunction, 1000, options);

        const task = module.scheduledTasks.get('test-task');
        expect(task.options).toEqual(options);
      });
    });

    describe('clearScheduledTask()', () => {
      test('should clear existing task successfully', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn();
        module.scheduleTask('test-task', taskFunction, 1000);

        expect(module.scheduledTasks.has('test-task')).toBe(true);

        const result = module.clearScheduledTask('test-task');
        expect(result).toBe(true);
        expect(module.scheduledTasks.has('test-task')).toBe(false);
      });

      test('should return false for non-existent task', () => {
        const result = module.clearScheduledTask('non-existent');
        expect(result).toBe(false);
      });

      test('should clear interval when task is running', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn();
        module.scheduleTask('test-task', taskFunction, 1000);

        const task = module.scheduledTasks.get('test-task');
        const intervalId = task.intervalId;
        expect(intervalId).not.toBeNull();

        // Mock clearInterval to verify it's called
        const originalClearInterval = global.clearInterval;
        global.clearInterval = jest.fn();

        module.clearScheduledTask('test-task');

        expect(global.clearInterval).toHaveBeenCalledWith(intervalId);

        // Restore original clearInterval
        global.clearInterval = originalClearInterval;
      });

      test('should log task clearing', () => {
        const taskFunction = jest.fn();
        module.scheduleTask('test-task', taskFunction, 1000);

        module.clearScheduledTask('test-task');

        expect(mockLogger.debug).toHaveBeenCalledWith(
          'Scheduled task cleared',
          expect.objectContaining({
            module: 'test-module',
            taskId: 'test-task'
          })
        );
      });
    });

    describe('clearAllScheduledTasks()', () => {
      test('should clear all scheduled tasks', async () => {
        const taskFunction1 = jest.fn();
        const taskFunction2 = jest.fn();
        const taskFunction3 = jest.fn();

        module.scheduleTask('task-1', taskFunction1, 1000);
        module.scheduleTask('task-2', taskFunction2, 2000);
        module.scheduleTask('task-3', taskFunction3, 3000);

        expect(module.scheduledTasks.size).toBe(3);

        await module.clearAllScheduledTasks();

        expect(module.scheduledTasks.size).toBe(0);
      });

      test('should clear intervals for running tasks', async () => {
        await module.initialize();
        await module.start();

        const taskFunction1 = jest.fn();
        const taskFunction2 = jest.fn();

        module.scheduleTask('task-1', taskFunction1, 1000);
        module.scheduleTask('task-2', taskFunction2, 2000);

        // Mock clearInterval to verify it's called
        const originalClearInterval = global.clearInterval;
        global.clearInterval = jest.fn();

        await module.clearAllScheduledTasks();

        expect(global.clearInterval).toHaveBeenCalledTimes(2);

        // Restore original clearInterval
        global.clearInterval = originalClearInterval;
      });

      test('should log clearing of all tasks', async () => {
        const taskFunction1 = jest.fn();
        const taskFunction2 = jest.fn();

        module.scheduleTask('task-1', taskFunction1, 1000);
        module.scheduleTask('task-2', taskFunction2, 2000);

        await module.clearAllScheduledTasks();

        expect(mockLogger.debug).toHaveBeenCalledWith(
          'All scheduled tasks cleared',
          expect.objectContaining({
            module: 'test-module',
            clearedCount: 2
          })
        );
      });
    });

    describe('Task Management Integration with Module Lifecycle', () => {
      test('should start scheduled tasks when module starts', async () => {
        const taskFunction = jest.fn();
        
        // Schedule task before module is started
        module.scheduleTask('test-task', taskFunction, 1000);
        
        const task = module.scheduledTasks.get('test-task');
        expect(task.intervalId).toBeNull();

        await module.initialize();
        await module.start();

        // Task should now have an interval ID
        expect(task.intervalId).not.toBeNull();
      });

      test('should clear all tasks when module stops', async () => {
        await module.initialize();
        await module.start();

        const taskFunction1 = jest.fn();
        const taskFunction2 = jest.fn();

        module.scheduleTask('task-1', taskFunction1, 1000);
        module.scheduleTask('task-2', taskFunction2, 2000);

        expect(module.scheduledTasks.size).toBe(2);

        await module.stop();

        expect(module.scheduledTasks.size).toBe(0);
      });

      test('should handle task clearing errors during stop', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn();
        module.scheduleTask('test-task', taskFunction, 1000);

        // Mock clearScheduledTask to throw error
        const originalClearAllScheduledTasks = module.clearAllScheduledTasks;
        module.clearAllScheduledTasks = jest.fn().mockRejectedValue(new Error('Clear failed'));

        // Stop should still complete despite task clearing error
        await expect(module.stop()).rejects.toThrow('Clear failed');

        // Restore original method
        module.clearAllScheduledTasks = originalClearAllScheduledTasks;
      });
    });

    describe('Task Status and Monitoring', () => {
      test('should include scheduled task count in status', () => {
        const taskFunction1 = jest.fn();
        const taskFunction2 = jest.fn();

        module.scheduleTask('task-1', taskFunction1, 1000);
        module.scheduleTask('task-2', taskFunction2, 2000);

        const status = module.getStatus();
        expect(status.scheduledTasks).toBe(2);
      });

      test('should track task execution statistics', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn().mockResolvedValue();
        module.scheduleTask('test-task', taskFunction, 1000);

        const task = module.scheduledTasks.get('test-task');

        // Execute task multiple times
        jest.advanceTimersByTime(3000);
        await Promise.resolve();

        expect(task.runCount).toBe(3);
        expect(task.errorCount).toBe(0);
        expect(task.lastRun).toBeInstanceOf(Date);
      });
    });
  });

  describe('Task Management Edge Cases', () => {
    describe('Async Task Handling', () => {
      test('should handle async tasks correctly', async () => {
        await module.initialize();
        await module.start();

        let resolveTask;
        const taskPromise = new Promise(resolve => {
          resolveTask = resolve;
        });

        const taskFunction = jest.fn().mockReturnValue(taskPromise);
        module.scheduleTask('async-task', taskFunction, 1000);

        // Trigger task execution
        jest.advanceTimersByTime(1000);

        // Task should be called but not yet completed
        expect(taskFunction).toHaveBeenCalledTimes(1);

        const task = module.scheduledTasks.get('async-task');
        expect(task.runCount).toBe(0); // Not incremented until promise resolves

        // Resolve the task
        resolveTask();
        await Promise.resolve();

        expect(task.runCount).toBe(1);
        expect(task.errorCount).toBe(0);
      });

      test('should handle async task errors', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn().mockRejectedValue(new Error('Async error'));
        module.scheduleTask('async-error-task', taskFunction, 1000);

        jest.advanceTimersByTime(1000);
        await Promise.resolve();

        const task = module.scheduledTasks.get('async-error-task');
        expect(task.runCount).toBe(0);
        expect(task.errorCount).toBe(1);
        expect(mockLogger.error).toHaveBeenCalledWith(
          'Scheduled task failed',
          expect.objectContaining({
            error: 'Async error'
          })
        );
      });
    });

    describe('Task Scheduling During Different Module States', () => {
      test('should allow scheduling tasks when module is disabled', () => {
        module.isEnabled = false;

        const taskFunction = jest.fn();
        const taskId = module.scheduleTask('disabled-task', taskFunction, 1000);

        expect(taskId).toBe('disabled-task');
        expect(module.scheduledTasks.has('disabled-task')).toBe(true);

        const task = module.scheduledTasks.get('disabled-task');
        expect(task.intervalId).toBeNull();
      });

      test('should start tasks when module is re-enabled', async () => {
        // Schedule task while disabled
        module.isEnabled = false;
        const taskFunction = jest.fn();
        module.scheduleTask('re-enable-task', taskFunction, 1000);

        await module.initialize();

        // Enable and start module
        await module.enable();

        const task = module.scheduledTasks.get('re-enable-task');
        expect(task.intervalId).not.toBeNull();
      });

      test('should clear tasks when module is disabled', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn();
        module.scheduleTask('disable-task', taskFunction, 1000);

        expect(module.scheduledTasks.size).toBe(1);

        await module.disable();

        expect(module.scheduledTasks.size).toBe(0);
      });
    });

    describe('Memory Management', () => {
      test('should not leak memory with many task schedules and clears', () => {
        const initialTaskCount = module.scheduledTasks.size;

        // Schedule many tasks
        for (let i = 0; i < 100; i++) {
          const taskFunction = jest.fn();
          module.scheduleTask(`task-${i}`, taskFunction, 1000);
        }

        expect(module.scheduledTasks.size).toBe(initialTaskCount + 100);

        // Clear all tasks
        for (let i = 0; i < 100; i++) {
          module.clearScheduledTask(`task-${i}`);
        }

        expect(module.scheduledTasks.size).toBe(initialTaskCount);
      });

      test('should handle rapid task scheduling and clearing', () => {
        const taskFunction = jest.fn();

        // Rapidly schedule and clear the same task ID
        for (let i = 0; i < 10; i++) {
          module.scheduleTask('rapid-task', taskFunction, 1000);
          expect(module.scheduledTasks.has('rapid-task')).toBe(true);

          module.clearScheduledTask('rapid-task');
          expect(module.scheduledTasks.has('rapid-task')).toBe(false);
        }
      });
    });

    describe('Task Execution Timing', () => {
      test('should handle very short intervals', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn();
        module.scheduleTask('fast-task', taskFunction, 10); // 10ms interval

        jest.advanceTimersByTime(100);
        expect(taskFunction).toHaveBeenCalledTimes(10);
      });

      test('should handle very long intervals', async () => {
        await module.initialize();
        await module.start();

        const taskFunction = jest.fn();
        module.scheduleTask('slow-task', taskFunction, 3600000); // 1 hour

        jest.advanceTimersByTime(1800000); // 30 minutes
        expect(taskFunction).not.toHaveBeenCalled();

        jest.advanceTimersByTime(1800000); // Another 30 minutes (total 1 hour)
        expect(taskFunction).toHaveBeenCalledTimes(1);
      });
    });

    describe('Concurrent Task Execution', () => {
      test('should handle multiple tasks with different intervals', async () => {
        await module.initialize();
        await module.start();

        const fastTask = jest.fn();
        const mediumTask = jest.fn();
        const slowTask = jest.fn();

        module.scheduleTask('fast', fastTask, 100);
        module.scheduleTask('medium', mediumTask, 300);
        module.scheduleTask('slow', slowTask, 1000);

        jest.advanceTimersByTime(1000);

        expect(fastTask).toHaveBeenCalledTimes(10);
        expect(mediumTask).toHaveBeenCalledTimes(3);
        expect(slowTask).toHaveBeenCalledTimes(1);
      });

      test('should maintain task independence during errors', async () => {
        await module.initialize();
        await module.start();

        const goodTask = jest.fn();
        const badTask = jest.fn().mockRejectedValue(new Error('Bad task'));

        module.scheduleTask('good', goodTask, 500);
        module.scheduleTask('bad', badTask, 500);

        jest.advanceTimersByTime(1000);
        await Promise.resolve();

        // Good task should continue working despite bad task errors
        expect(goodTask).toHaveBeenCalledTimes(2);
        expect(badTask).toHaveBeenCalledTimes(2);

        const goodTaskData = module.scheduledTasks.get('good');
        const badTaskData = module.scheduledTasks.get('bad');

        expect(goodTaskData.runCount).toBe(2);
        expect(goodTaskData.errorCount).toBe(0);
        expect(badTaskData.runCount).toBe(0);
        expect(badTaskData.errorCount).toBe(2);
      });
    });
  });

  describe('Integration with Module Lifecycle', () => {
    test('should properly initialize task system during module initialization', async () => {
      const taskFunction = jest.fn();
      module.scheduleTask('init-task', taskFunction, 1000);

      expect(module.scheduledTasks.size).toBe(1);

      await module.initialize();

      // Task should still exist but not be running
      expect(module.scheduledTasks.size).toBe(1);
      const task = module.scheduledTasks.get('init-task');
      expect(task.intervalId).toBeNull();
    });

    test('should handle task management during module restart', async () => {
      await module.initialize();
      await module.start();

      const taskFunction = jest.fn();
      module.scheduleTask('restart-task', taskFunction, 1000);

      // Verify task is running
      jest.advanceTimersByTime(1000);
      expect(taskFunction).toHaveBeenCalledTimes(1);

      // Stop module
      await module.stop();
      expect(module.scheduledTasks.size).toBe(0);

      // Restart module
      await module.start();

      // Schedule new task
      module.scheduleTask('new-restart-task', taskFunction, 1000);
      jest.advanceTimersByTime(1000);
      expect(taskFunction).toHaveBeenCalledTimes(2);
    });
  });
});
