const { app, BrowserWindow, ipc<PERSON>ain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { CoreLogger } = require('../core/logger/CoreLogger');

// Increase the max listeners for process to avoid memory leak warnings in plugin-heavy environments
process.setMaxListeners(50);

// Set log directory to ./logs/electron (relative to project root)
const projectRoot = path.resolve(__dirname, '../..');
const electronLogDir = path.join(projectRoot, 'logs', 'electron');
if (!fs.existsSync(electronLogDir)) {
  fs.mkdirSync(electronLogDir, { recursive: true, mode: 0o700 });
}
const logOptions = { logDir: electronLogDir };
const log = new CoreLogger(logOptions);

// Test log to confirm electron logging is working
log.INFO('Electron application starting', {
  logDir: electronLogDir,
  isDev: process.env.ELECTRON_IS_DEV === 'true'
});

// Security: Disable node integration by default
const isDev = process.env.ELECTRON_IS_DEV === 'true';

class LifeboardApp {
  constructor() {
    this.mainWindow = null;
    this.serviceManager = null;
    this.setupApp();
  }

  setupApp() {
    // Security: Prevent new window creation
    // Note: Temporarily disabled - may need to be updated for current Electron version
    // app.on('web-contents-created', (event, contents) => {
    //   if (contents) {
    //     contents.on('new-window', (event, navigationUrl) => {
    //       event.preventDefault();
    //       shell.openExternal(navigationUrl);
    //     });
    //   }
    // });

    // App event handlers
    app.whenReady().then(() => {
      this.createMainWindow();
      this.initializeServiceManager();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Setup IPC handlers
    this.setupIpcHandlers();
  }

  createMainWindow() {
    log.INFO('Creating main window');

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false, // Start hidden, show when ready
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: !isDev, // Disable in dev for local file access
        allowRunningInsecureContent: false,
        experimentalFeatures: false
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    });

    // Load the web UI
    const webUIPath = isDev
      ? 'http://localhost:9820' // Dev: Use running web server
      : `file://${path.join(__dirname, '../webui/index.html')}`; // Prod: Local files

    this.mainWindow.loadURL(webUIPath);

    // Window event handlers
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      log.INFO('Main window shown');
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Development tools
    if (isDev) {
      this.mainWindow.webContents.openDevTools();
    }

    // Security: Prevent navigation to external URLs
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl);

      if (parsedUrl.origin !== 'http://localhost:9820' && !navigationUrl.startsWith('file://')) {
        event.preventDefault();
        log.WARN('Blocked navigation attempt', { navigationUrl });
      }
    });
  }

  initializeServiceManager() {
    log.INFO('Initializing service manager');

    try {
      const { ServiceManager } = require('./service-manager');
      log.INFO('ServiceManager class loaded successfully');

      this.serviceManager = new ServiceManager(this, { logDir: electronLogDir });
      log.INFO('ServiceManager instance created successfully');

      // Initialize service manager
      log.INFO('Starting service manager initialization');
      this.serviceManager.initialize().then(() => {
        log.INFO('Service manager initialization completed');
        
        // Start all services
        return this.serviceManager.start();
      }).then(() => {
        log.INFO('All services started successfully');
      }).catch((error) => {
        log.ERROR('Error during service manager startup:', {
          error: error.message,
          stack: error.stack,
          phase: 'startup'
        });
      });

    } catch (error) {
      log.ERROR('Error during service manager initialization:', {
        error: error.message,
        stack: error.stack,
        phase: 'initialization'
      });
      throw error;
    }
  }

  setupIpcHandlers() {
    // Service management IPC handlers
    ipcMain.handle('services:list', async () => {
      return this.serviceManager ? this.serviceManager.getServiceStats() : {};
    });

    ipcMain.handle('services:get-status', async (event, serviceName) => {
      return this.serviceManager ? this.serviceManager.getServiceStatus(serviceName) : null;
    });

    ipcMain.handle('services:start', async (event, serviceName) => {
      try {
        return this.serviceManager ? await this.serviceManager.startService(serviceName) : false;
      } catch (error) {
        log.ERROR('IPC: Service start error', { serviceName, error: error.message });
        return false;
      }
    });

    ipcMain.handle('services:stop', async (event, serviceName) => {
      try {
        return this.serviceManager ? await this.serviceManager.stopService(serviceName) : false;
      } catch (error) {
        log.ERROR('IPC: Service stop error', { serviceName, error: error.message });
        return false;
      }
    });

    ipcMain.handle('services:restart', async (event, serviceName) => {
      try {
        return this.serviceManager ? await this.serviceManager.restartService(serviceName) : false;
      } catch (error) {
        log.ERROR('IPC: Service restart error', { serviceName, error: error.message });
        return false;
      }
    });

    // Service statistics and monitoring
    ipcMain.handle('services:stats', async () => {
      return this.serviceManager ? this.serviceManager.getServiceStats() : {};
    });

    // App info IPC
    ipcMain.handle('app:version', () => {
      return app.getVersion();
    });

    ipcMain.handle('app:platform', () => {
      return process.platform;
    });

    // Logging IPC
    ipcMain.handle('log:info', (event, message) => {
      log.INFO(`[Renderer] ${message}`, { source: 'renderer' });
    });

    ipcMain.handle('log:error', (event, message) => {
      log.ERROR(`[Renderer] ${message}`, { source: 'renderer' });
    });

    ipcMain.handle('log:warn', (event, message) => {
      log.WARN(`[Renderer] ${message}`, { source: 'renderer' });
    });

    // Show native dialogs
    ipcMain.handle('dialog:showError', async (event, title, content) => {
      dialog.showErrorBox(title, content);
    });

    ipcMain.handle('dialog:showMessage', async (event, options) => {
      return dialog.showMessageBox(this.mainWindow, options);
    });
  }

  getMainWindow() {
    return this.mainWindow;
  }

  getServiceManager() {
    return this.serviceManager;
  }
}

// Create and start the app
const lifeboardApp = new LifeboardApp();

// Export for testing
module.exports = LifeboardApp;
