const BaseService = require('./base-service');
const { createClient } = require('@supabase/supabase-js');

/**
 * Limitless Service
 * 
 * Provides lifelog data synchronization from Limitless AI to Lifeboard.
 * Transformed from plugin architecture to native service.
 */
class LimitlessService extends BaseService {
  constructor(options = {}) {
    super('limitless', options);
    
    // Service-specific state
    this.limitlessClient = null;
    this.supabaseClient = null;
    this.syncTaskId = null;
    this.lastSyncTime = null;
    this.syncStats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      totalRecordsProcessed: 0,
      lastSyncDuration: 0
    };
  }

  /**
   * Load configuration from environment variables
   * 
   * @returns {Promise<void>}
   */
  async loadConfiguration() {
    await super.loadConfiguration();
    
    this.config = {
      ...this.config,
      apiKey: process.env.LIMITLESS_API_KEY,
      syncInterval: parseInt(process.env.LIMITLESS_SYNC_INTERVAL || '14400'), // 4 hours in seconds
      enabled: process.env.LIMITLESS_ENABLED === 'true',
      baseUrl: process.env.LIMITLESS_BASE_URL || 'https://api.limitless.ai',
      batchSize: parseInt(process.env.LIMITLESS_BATCH_SIZE || '100'),
      maxRetries: parseInt(process.env.LIMITLESS_MAX_RETRIES || '3'),
      retryDelay: parseInt(process.env.LIMITLESS_RETRY_DELAY || '5000')
    };
  }

  /**
   * Validate service configuration
   * 
   * @returns {Promise<void>}
   */
  async validateConfiguration() {
    await super.validateConfiguration();
    
    if (!this.config.apiKey) {
      throw new Error('LIMITLESS_API_KEY environment variable is required');
    }
    
    if (this.config.syncInterval < 3600) {
      throw new Error('LIMITLESS_SYNC_INTERVAL must be at least 3600 seconds (1 hour)');
    }
    
    if (!this.config.baseUrl) {
      throw new Error('LIMITLESS_BASE_URL is required');
    }
  }

  /**
   * Initialize database connections
   * 
   * @returns {Promise<void>}
   */
  async initializeDatabase() {
    await super.initializeDatabase();
    
    // Initialize Supabase client for database operations
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are required');
    }
    
    this.supabaseClient = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test database connection
    try {
      const { data, error } = await this.supabaseClient
        .from('limitless_lifelogs')
        .select('count')
        .limit(1);
      
      if (error) {
        throw error;
      }
      
      await this.logger.info('Limitless service: Database connection established');
    } catch (error) {
      await this.logger.error('Limitless service: Database connection failed', error);
      throw new Error('Failed to connect to database');
    }
  }

  /**
   * Service-specific initialization
   * 
   * @returns {Promise<void>}
   */
  async onInitialize() {
    await super.onInitialize();
    
    // Initialize Limitless API client
    this.limitlessClient = new LimitlessAPIClient({
      apiKey: this.config.apiKey,
      baseUrl: this.config.baseUrl,
      maxRetries: this.config.maxRetries,
      retryDelay: this.config.retryDelay,
      logger: this.logger
    });
    
    // Validate API key
    try {
      const isValid = await this.limitlessClient.validateApiKey();
      if (!isValid) {
        throw new Error('Invalid Limitless API key');
      }
      await this.logger.info('Limitless service: API key validated successfully');
    } catch (error) {
      await this.logger.error('Limitless service: API key validation failed', error);
      throw error;
    }
    
    // Load last sync time from database
    await this.loadLastSyncTime();
  }

  /**
   * Start service-specific functionality
   * 
   * @returns {Promise<void>}
   */
  async onStart() {
    await super.onStart();
    
    // Schedule sync task
    const syncIntervalMs = this.config.syncInterval * 1000;
    this.syncTaskId = this.scheduleTask(
      'lifelog-sync',
      () => this.performSync(),
      syncIntervalMs
    );
    
    await this.logger.info('Limitless service: Sync task scheduled', {
      intervalSeconds: this.config.syncInterval,
      nextSync: new Date(Date.now() + syncIntervalMs)
    });
    
    // Perform initial sync if it's been more than sync interval since last sync
    if (this.shouldPerformInitialSync()) {
      await this.logger.info('Limitless service: Performing initial sync');
      setImmediate(() => this.performSync());
    }
  }

  /**
   * Stop service-specific functionality
   * 
   * @returns {Promise<void>}
   */
  async onStop() {
    await super.onStop();
    
    // Remove sync task
    if (this.syncTaskId) {
      this.removeScheduledTask(this.syncTaskId);
      this.syncTaskId = null;
    }
    
    await this.logger.info('Limitless service: Sync task stopped');
  }

  /**
   * Perform lifelog synchronization
   * 
   * @returns {Promise<Object>} Sync result
   */
  async performSync() {
    const startTime = Date.now();
    const syncId = `sync-${Date.now()}`;
    
    try {
      await this.logger.info('Limitless service: Starting sync', { syncId });
      
      this.syncStats.totalSyncs++;
      
      // Get cursor for pagination
      const cursor = await this.getLastSyncCursor();
      
      // Fetch lifelogs from Limitless API
      const lifelogs = await this.limitlessClient.fetchLifelogs({
        cursor,
        limit: this.config.batchSize
      });
      
      if (lifelogs.length === 0) {
        await this.logger.info('Limitless service: No new lifelogs to sync', { syncId });
        this.syncStats.successfulSyncs++;
        return { success: true, processed: 0, message: 'No new data' };
      }
      
      // Process and store lifelogs
      const processed = await this.processLifelogs(lifelogs);
      
      // Update sync statistics
      this.syncStats.successfulSyncs++;
      this.syncStats.totalRecordsProcessed += processed;
      this.syncStats.lastSyncDuration = Date.now() - startTime;
      this.lastSyncTime = new Date();
      
      // Update last sync time in database
      await this.updateLastSyncTime();
      
      await this.logger.info('Limitless service: Sync completed successfully', {
        syncId,
        processed,
        duration: this.syncStats.lastSyncDuration
      });
      
      return {
        success: true,
        processed,
        duration: this.syncStats.lastSyncDuration,
        message: `Processed ${processed} lifelogs`
      };
      
    } catch (error) {
      this.syncStats.failedSyncs++;
      await this.logger.error('Limitless service: Sync failed', error, { syncId });
      
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Process lifelogs and store in database
   * 
   * @param {Array} lifelogs - Array of lifelog entries
   * @returns {Promise<number>} Number of processed records
   */
  async processLifelogs(lifelogs) {
    let processed = 0;
    
    for (const lifelog of lifelogs) {
      try {
        // Transform lifelog data
        const transformedData = this.transformLifelogData(lifelog);
        
        // Upsert into database
        const { error } = await this.supabaseClient
          .from('limitless_lifelogs')
          .upsert(transformedData, {
            onConflict: 'external_id'
          });
        
        if (error) {
          await this.logger.error('Limitless service: Failed to insert lifelog', error, {
            lifelogId: lifelog.id
          });
          continue;
        }
        
        processed++;
        
      } catch (error) {
        await this.logger.error('Limitless service: Failed to process lifelog', error, {
          lifelogId: lifelog.id
        });
      }
    }
    
    return processed;
  }

  /**
   * Transform lifelog data for database storage
   * 
   * @param {Object} lifelog - Raw lifelog data from Limitless API
   * @returns {Object} Transformed data for database
   */
  transformLifelogData(lifelog) {
    return {
      external_id: lifelog.id,
      timestamp: lifelog.timestamp,
      content: lifelog.content,
      transcription: lifelog.transcription,
      summary: lifelog.summary,
      category: lifelog.category,
      confidence: lifelog.confidence,
      duration: lifelog.duration,
      metadata: lifelog.metadata || {},
      raw_data: lifelog,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Get last sync cursor for pagination
   * 
   * @returns {Promise<string|null>} Cursor or null
   */
  async getLastSyncCursor() {
    try {
      const { data, error } = await this.supabaseClient
        .from('limitless_lifelogs')
        .select('external_id, timestamp')
        .order('timestamp', { ascending: false })
        .limit(1);
      
      if (error) {
        throw error;
      }
      
      return data && data.length > 0 ? data[0].external_id : null;
    } catch (error) {
      await this.logger.error('Limitless service: Failed to get last sync cursor', error);
      return null;
    }
  }

  /**
   * Load last sync time from database
   * 
   * @returns {Promise<void>}
   */
  async loadLastSyncTime() {
    try {
      const { data, error } = await this.supabaseClient
        .from('service_metadata')
        .select('value')
        .eq('service_name', 'limitless')
        .eq('key', 'last_sync_time')
        .single();
      
      if (error && error.code !== 'PGRST116') { // Not found is OK
        throw error;
      }
      
      if (data && data.value) {
        this.lastSyncTime = new Date(data.value);
      }
    } catch (error) {
      await this.logger.error('Limitless service: Failed to load last sync time', error);
    }
  }

  /**
   * Update last sync time in database
   * 
   * @returns {Promise<void>}
   */
  async updateLastSyncTime() {
    try {
      const { error } = await this.supabaseClient
        .from('service_metadata')
        .upsert({
          service_name: 'limitless',
          key: 'last_sync_time',
          value: this.lastSyncTime.toISOString(),
          updated_at: new Date()
        }, {
          onConflict: 'service_name,key'
        });
      
      if (error) {
        throw error;
      }
    } catch (error) {
      await this.logger.error('Limitless service: Failed to update last sync time', error);
    }
  }

  /**
   * Check if initial sync should be performed
   * 
   * @returns {boolean} Whether to perform initial sync
   */
  shouldPerformInitialSync() {
    if (!this.lastSyncTime) {
      return true;
    }
    
    const timeSinceLastSync = Date.now() - this.lastSyncTime.getTime();
    const syncIntervalMs = this.config.syncInterval * 1000;
    
    return timeSinceLastSync >= syncIntervalMs;
  }

  /**
   * Get service statistics
   * 
   * @returns {Object} Service statistics
   */
  getServiceStats() {
    return {
      ...this.syncStats,
      lastSyncTime: this.lastSyncTime,
      nextSyncTime: this.syncTaskId ? 
        new Date(Date.now() + (this.config.syncInterval * 1000)) : null,
      configuration: this.getSafeConfig()
    };
  }

  /**
   * Perform manual sync
   * 
   * @returns {Promise<Object>} Sync result
   */
  async performManualSync() {
    await this.logger.info('Limitless service: Manual sync requested');
    return await this.performSync();
  }

  /**
   * Validate API key
   * 
   * @returns {Promise<boolean>} Whether API key is valid
   */
  async validateApiKey() {
    try {
      return await this.limitlessClient.validateApiKey();
    } catch (error) {
      await this.logger.error('Limitless service: API key validation failed', error);
      return false;
    }
  }
}

/**
 * Limitless API Client
 * 
 * Handles communication with Limitless AI API
 */
class LimitlessAPIClient {
  constructor(options = {}) {
    this.apiKey = options.apiKey;
    this.baseUrl = options.baseUrl;
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 5000;
    this.logger = options.logger;
  }

  /**
   * Validate API key
   * 
   * @returns {Promise<boolean>} Whether API key is valid
   */
  async validateApiKey() {
    try {
      const response = await this.makeRequest('/auth/validate');
      return response.valid === true;
    } catch (error) {
      if (this.logger) {
        await this.logger.error('Limitless API: API key validation failed', error);
      }
      return false;
    }
  }

  /**
   * Fetch lifelogs from API
   * 
   * @param {Object} options - Fetch options
   * @param {string} options.cursor - Pagination cursor
   * @param {number} options.limit - Number of records to fetch
   * @returns {Promise<Array>} Array of lifelogs
   */
  async fetchLifelogs(options = {}) {
    const params = new URLSearchParams();
    
    if (options.cursor) {
      params.append('cursor', options.cursor);
    }
    
    if (options.limit) {
      params.append('limit', options.limit.toString());
    }
    
    const url = `/lifelogs?${params.toString()}`;
    const response = await this.makeRequest(url);
    
    return response.lifelogs || [];
  }

  /**
   * Make HTTP request to Limitless API
   * 
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'User-Agent': 'Lifeboard/1.0'
    };

    let lastError;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(url, {
          method: options.method || 'GET',
          headers,
          body: options.body ? JSON.stringify(options.body) : undefined
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
        
      } catch (error) {
        lastError = error;
        
        if (this.logger) {
          await this.logger.warn(`Limitless API: Request failed (attempt ${attempt}/${this.maxRetries})`, error);
        }
        
        if (attempt < this.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        }
      }
    }
    
    throw lastError;
  }
}

module.exports = LimitlessService;