const BaseService = require('./base-service');
const { createClient } = require('@supabase/supabase-js');

/**
 * Weather Service
 * 
 * Provides weather data synchronization and forecasting.
 * Transformed from plugin architecture to native service.
 */
class WeatherService extends BaseService {
  constructor(options = {}) {
    super('weather', options);
    
    // Service-specific state
    this.weatherClient = null;
    this.supabaseClient = null;
    this.syncTaskId = null;
    this.lastSyncTime = null;
    this.syncStats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastSyncDuration: 0,
      forecastDays: 5
    };
  }

  /**
   * Load configuration from environment variables
   * 
   * @returns {Promise<void>}
   */
  async loadConfiguration() {
    await super.loadConfiguration();
    
    this.config = {
      ...this.config,
      apiKey: process.env.WEATHER_API_KEY,
      latitude: parseFloat(process.env.WEATHER_LATITUDE),
      longitude: parseFloat(process.env.WEATHER_LONGITUDE),
      units: process.env.WEATHER_UNITS || 'metric',
      syncInterval: parseInt(process.env.WEATHER_SYNC_INTERVAL || '43200'), // 12 hours in seconds
      enabled: process.env.WEATHER_ENABLED === 'true',
      provider: process.env.WEATHER_PROVIDER || 'openweathermap',
      forecastDays: parseInt(process.env.WEATHER_FORECAST_DAYS || '5'),
      maxRetries: parseInt(process.env.WEATHER_MAX_RETRIES || '3'),
      retryDelay: parseInt(process.env.WEATHER_RETRY_DELAY || '5000')
    };
  }

  /**
   * Validate service configuration
   * 
   * @returns {Promise<void>}
   */
  async validateConfiguration() {
    await super.validateConfiguration();
    
    if (!this.config.apiKey) {
      throw new Error('WEATHER_API_KEY environment variable is required');
    }
    
    if (isNaN(this.config.latitude) || isNaN(this.config.longitude)) {
      throw new Error('WEATHER_LATITUDE and WEATHER_LONGITUDE must be valid numbers');
    }
    
    if (!['metric', 'imperial'].includes(this.config.units)) {
      throw new Error('WEATHER_UNITS must be either "metric" or "imperial"');
    }
    
    if (this.config.syncInterval < 1800) {
      throw new Error('WEATHER_SYNC_INTERVAL must be at least 1800 seconds (30 minutes)');
    }
  }

  /**
   * Initialize database connections
   * 
   * @returns {Promise<void>}
   */
  async initializeDatabase() {
    await super.initializeDatabase();
    
    // Initialize Supabase client for database operations
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are required');
    }
    
    this.supabaseClient = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test database connection
    try {
      const { data, error } = await this.supabaseClient
        .from('weather_data')
        .select('count')
        .limit(1);
      
      if (error) {
        throw error;
      }
      
      await this.logger.info('Weather service: Database connection established');
    } catch (error) {
      await this.logger.error('Weather service: Database connection failed', error);
      throw new Error('Failed to connect to database');
    }
  }

  /**
   * Service-specific initialization
   * 
   * @returns {Promise<void>}
   */
  async onInitialize() {
    await super.onInitialize();
    
    // Initialize weather API client
    this.weatherClient = new WeatherAPIClient({
      apiKey: this.config.apiKey,
      provider: this.config.provider,
      maxRetries: this.config.maxRetries,
      retryDelay: this.config.retryDelay,
      logger: this.logger
    });
    
    // Validate API key
    try {
      const isValid = await this.weatherClient.validateApiKey();
      if (!isValid) {
        throw new Error('Invalid weather API key');
      }
      await this.logger.info('Weather service: API key validated successfully');
    } catch (error) {
      await this.logger.error('Weather service: API key validation failed', error);
      throw error;
    }
    
    // Load last sync time from database
    await this.loadLastSyncTime();
  }

  /**
   * Start service-specific functionality
   * 
   * @returns {Promise<void>}
   */
  async onStart() {
    await super.onStart();
    
    // Schedule sync task
    const syncIntervalMs = this.config.syncInterval * 1000;
    this.syncTaskId = this.scheduleTask(
      'weather-sync',
      () => this.performSync(),
      syncIntervalMs
    );
    
    await this.logger.info('Weather service: Sync task scheduled', {
      intervalSeconds: this.config.syncInterval,
      nextSync: new Date(Date.now() + syncIntervalMs)
    });
    
    // Perform initial sync if it's been more than sync interval since last sync
    if (this.shouldPerformInitialSync()) {
      await this.logger.info('Weather service: Performing initial sync');
      setImmediate(() => this.performSync());
    }
  }

  /**
   * Stop service-specific functionality
   * 
   * @returns {Promise<void>}
   */
  async onStop() {
    await super.onStop();
    
    // Remove sync task
    if (this.syncTaskId) {
      this.removeScheduledTask(this.syncTaskId);
      this.syncTaskId = null;
    }
    
    await this.logger.info('Weather service: Sync task stopped');
  }

  /**
   * Perform weather data synchronization
   * 
   * @returns {Promise<Object>} Sync result
   */
  async performSync() {
    const startTime = Date.now();
    const syncId = `sync-${Date.now()}`;
    
    try {
      await this.logger.info('Weather service: Starting sync', { syncId });
      
      this.syncStats.totalSyncs++;
      
      // Fetch current weather
      const currentWeather = await this.weatherClient.getCurrentWeather(
        this.config.latitude,
        this.config.longitude,
        this.config.units
      );
      
      // Fetch forecast
      const forecast = await this.weatherClient.getForecast(
        this.config.latitude,
        this.config.longitude,
        this.config.units,
        this.config.forecastDays
      );
      
      // Store weather data
      await this.storeWeatherData(currentWeather, forecast);
      
      // Update sync statistics
      this.syncStats.successfulSyncs++;
      this.syncStats.lastSyncDuration = Date.now() - startTime;
      this.lastSyncTime = new Date();
      
      // Update last sync time in database
      await this.updateLastSyncTime();
      
      await this.logger.info('Weather service: Sync completed successfully', {
        syncId,
        duration: this.syncStats.lastSyncDuration,
        forecastDays: this.config.forecastDays
      });
      
      return {
        success: true,
        duration: this.syncStats.lastSyncDuration,
        message: `Updated weather data and ${this.config.forecastDays}-day forecast`
      };
      
    } catch (error) {
      this.syncStats.failedSyncs++;
      await this.logger.error('Weather service: Sync failed', error, { syncId });
      
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Store weather data in database
   * 
   * @param {Object} currentWeather - Current weather data
   * @param {Array} forecast - Forecast data
   * @returns {Promise<void>}
   */
  async storeWeatherData(currentWeather, forecast) {
    // Store current weather
    const currentData = this.transformCurrentWeatherData(currentWeather);
    const { error: currentError } = await this.supabaseClient
      .from('weather_data')
      .upsert(currentData, {
        onConflict: 'location_key,timestamp'
      });
    
    if (currentError) {
      throw new Error(`Failed to store current weather: ${currentError.message}`);
    }
    
    // Store forecast data
    for (const forecastItem of forecast) {
      const forecastData = this.transformForecastData(forecastItem);
      const { error: forecastError } = await this.supabaseClient
        .from('weather_forecast')
        .upsert(forecastData, {
          onConflict: 'location_key,forecast_date'
        });
      
      if (forecastError) {
        await this.logger.error('Weather service: Failed to store forecast item', forecastError);
      }
    }
  }

  /**
   * Transform current weather data for database storage
   * 
   * @param {Object} weatherData - Raw weather data from API
   * @returns {Object} Transformed data for database
   */
  transformCurrentWeatherData(weatherData) {
    return {
      location_key: this.getLocationKey(),
      timestamp: new Date(),
      temperature: weatherData.temperature,
      feels_like: weatherData.feels_like,
      humidity: weatherData.humidity,
      pressure: weatherData.pressure,
      wind_speed: weatherData.wind_speed,
      wind_direction: weatherData.wind_direction,
      visibility: weatherData.visibility,
      uv_index: weatherData.uv_index,
      condition: weatherData.condition,
      condition_code: weatherData.condition_code,
      icon: weatherData.icon,
      sunrise: weatherData.sunrise,
      sunset: weatherData.sunset,
      units: this.config.units,
      raw_data: weatherData,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Transform forecast data for database storage
   * 
   * @param {Object} forecastItem - Raw forecast data from API
   * @returns {Object} Transformed data for database
   */
  transformForecastData(forecastItem) {
    return {
      location_key: this.getLocationKey(),
      forecast_date: forecastItem.date,
      temperature_min: forecastItem.temp_min,
      temperature_max: forecastItem.temp_max,
      humidity: forecastItem.humidity,
      pressure: forecastItem.pressure,
      wind_speed: forecastItem.wind_speed,
      wind_direction: forecastItem.wind_direction,
      condition: forecastItem.condition,
      condition_code: forecastItem.condition_code,
      icon: forecastItem.icon,
      precipitation_probability: forecastItem.precipitation_probability,
      precipitation_amount: forecastItem.precipitation_amount,
      units: this.config.units,
      raw_data: forecastItem,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Get location key for database storage
   * 
   * @returns {string} Location key
   */
  getLocationKey() {
    return `${this.config.latitude.toFixed(4)},${this.config.longitude.toFixed(4)}`;
  }

  /**
   * Load last sync time from database
   * 
   * @returns {Promise<void>}
   */
  async loadLastSyncTime() {
    try {
      const { data, error } = await this.supabaseClient
        .from('service_metadata')
        .select('value')
        .eq('service_name', 'weather')
        .eq('key', 'last_sync_time')
        .single();
      
      if (error && error.code !== 'PGRST116') { // Not found is OK
        throw error;
      }
      
      if (data && data.value) {
        this.lastSyncTime = new Date(data.value);
      }
    } catch (error) {
      await this.logger.error('Weather service: Failed to load last sync time', error);
    }
  }

  /**
   * Update last sync time in database
   * 
   * @returns {Promise<void>}
   */
  async updateLastSyncTime() {
    try {
      const { error } = await this.supabaseClient
        .from('service_metadata')
        .upsert({
          service_name: 'weather',
          key: 'last_sync_time',
          value: this.lastSyncTime.toISOString(),
          updated_at: new Date()
        }, {
          onConflict: 'service_name,key'
        });
      
      if (error) {
        throw error;
      }
    } catch (error) {
      await this.logger.error('Weather service: Failed to update last sync time', error);
    }
  }

  /**
   * Check if initial sync should be performed
   * 
   * @returns {boolean} Whether to perform initial sync
   */
  shouldPerformInitialSync() {
    if (!this.lastSyncTime) {
      return true;
    }
    
    const timeSinceLastSync = Date.now() - this.lastSyncTime.getTime();
    const syncIntervalMs = this.config.syncInterval * 1000;
    
    return timeSinceLastSync >= syncIntervalMs;
  }

  /**
   * Get current weather data
   * 
   * @returns {Promise<Object>} Current weather data
   */
  async getCurrentWeather() {
    try {
      const { data, error } = await this.supabaseClient
        .from('weather_data')
        .select('*')
        .eq('location_key', this.getLocationKey())
        .order('timestamp', { ascending: false })
        .limit(1);
      
      if (error) {
        throw error;
      }
      
      return data && data.length > 0 ? data[0] : null;
    } catch (error) {
      await this.logger.error('Weather service: Failed to get current weather', error);
      throw error;
    }
  }

  /**
   * Get weather forecast
   * 
   * @param {number} days - Number of forecast days
   * @returns {Promise<Array>} Forecast data
   */
  async getForecast(days = 5) {
    try {
      const { data, error } = await this.supabaseClient
        .from('weather_forecast')
        .select('*')
        .eq('location_key', this.getLocationKey())
        .gte('forecast_date', new Date().toISOString().split('T')[0])
        .order('forecast_date', { ascending: true })
        .limit(days);
      
      if (error) {
        throw error;
      }
      
      return data || [];
    } catch (error) {
      await this.logger.error('Weather service: Failed to get forecast', error);
      throw error;
    }
  }

  /**
   * Get service statistics
   * 
   * @returns {Object} Service statistics
   */
  getServiceStats() {
    return {
      ...this.syncStats,
      lastSyncTime: this.lastSyncTime,
      nextSyncTime: this.syncTaskId ? 
        new Date(Date.now() + (this.config.syncInterval * 1000)) : null,
      location: {
        latitude: this.config.latitude,
        longitude: this.config.longitude
      },
      configuration: this.getSafeConfig()
    };
  }

  /**
   * Perform manual sync
   * 
   * @returns {Promise<Object>} Sync result
   */
  async performManualSync() {
    await this.logger.info('Weather service: Manual sync requested');
    return await this.performSync();
  }
}

/**
 * Weather API Client
 * 
 * Handles communication with weather API providers
 */
class WeatherAPIClient {
  constructor(options = {}) {
    this.apiKey = options.apiKey;
    this.provider = options.provider || 'openweathermap';
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 5000;
    this.logger = options.logger;
    
    // API endpoints for different providers
    this.endpoints = {
      openweathermap: {
        baseUrl: 'https://api.openweathermap.org/data/2.5',
        current: '/weather',
        forecast: '/forecast'
      }
    };
  }

  /**
   * Validate API key
   * 
   * @returns {Promise<boolean>} Whether API key is valid
   */
  async validateApiKey() {
    try {
      await this.getCurrentWeather(0, 0, 'metric');
      return true;
    } catch (error) {
      if (this.logger) {
        await this.logger.error('Weather API: API key validation failed', error);
      }
      return false;
    }
  }

  /**
   * Get current weather
   * 
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @param {string} units - Units (metric/imperial)
   * @returns {Promise<Object>} Current weather data
   */
  async getCurrentWeather(lat, lon, units = 'metric') {
    const endpoint = this.endpoints[this.provider];
    const url = `${endpoint.baseUrl}${endpoint.current}`;
    
    const params = {
      lat: lat.toString(),
      lon: lon.toString(),
      units,
      appid: this.apiKey
    };
    
    const response = await this.makeRequest(url, { params });
    return this.transformCurrentWeatherResponse(response);
  }

  /**
   * Get weather forecast
   * 
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @param {string} units - Units (metric/imperial)
   * @param {number} days - Number of forecast days
   * @returns {Promise<Array>} Forecast data
   */
  async getForecast(lat, lon, units = 'metric', days = 5) {
    const endpoint = this.endpoints[this.provider];
    const url = `${endpoint.baseUrl}${endpoint.forecast}`;
    
    const params = {
      lat: lat.toString(),
      lon: lon.toString(),
      units,
      appid: this.apiKey,
      cnt: (days * 8).toString() // 8 forecasts per day (3-hour intervals)
    };
    
    const response = await this.makeRequest(url, { params });
    return this.transformForecastResponse(response, days);
  }

  /**
   * Transform current weather API response
   * 
   * @param {Object} response - API response
   * @returns {Object} Transformed weather data
   */
  transformCurrentWeatherResponse(response) {
    return {
      temperature: response.main.temp,
      feels_like: response.main.feels_like,
      humidity: response.main.humidity,
      pressure: response.main.pressure,
      wind_speed: response.wind?.speed || 0,
      wind_direction: response.wind?.deg || 0,
      visibility: response.visibility || 0,
      uv_index: response.uvi || 0,
      condition: response.weather[0]?.description || '',
      condition_code: response.weather[0]?.id || 0,
      icon: response.weather[0]?.icon || '',
      sunrise: response.sys?.sunrise ? new Date(response.sys.sunrise * 1000) : null,
      sunset: response.sys?.sunset ? new Date(response.sys.sunset * 1000) : null
    };
  }

  /**
   * Transform forecast API response
   * 
   * @param {Object} response - API response
   * @param {number} days - Number of forecast days
   * @returns {Array} Transformed forecast data
   */
  transformForecastResponse(response, days) {
    const forecasts = [];
    const dailyData = {};
    
    // Group forecasts by date
    for (const item of response.list) {
      const date = new Date(item.dt * 1000).toISOString().split('T')[0];
      
      if (!dailyData[date]) {
        dailyData[date] = {
          date,
          temps: [],
          conditions: [],
          humidity: [],
          pressure: [],
          wind_speed: [],
          wind_direction: [],
          precipitation: []
        };
      }
      
      dailyData[date].temps.push(item.main.temp);
      dailyData[date].conditions.push(item.weather[0]);
      dailyData[date].humidity.push(item.main.humidity);
      dailyData[date].pressure.push(item.main.pressure);
      dailyData[date].wind_speed.push(item.wind?.speed || 0);
      dailyData[date].wind_direction.push(item.wind?.deg || 0);
      dailyData[date].precipitation.push(item.pop || 0);
    }
    
    // Create daily forecasts
    for (const [date, data] of Object.entries(dailyData)) {
      if (forecasts.length >= days) break;
      
      const condition = data.conditions[0] || {};
      
      forecasts.push({
        date,
        temp_min: Math.min(...data.temps),
        temp_max: Math.max(...data.temps),
        humidity: Math.round(data.humidity.reduce((a, b) => a + b) / data.humidity.length),
        pressure: Math.round(data.pressure.reduce((a, b) => a + b) / data.pressure.length),
        wind_speed: Math.round(data.wind_speed.reduce((a, b) => a + b) / data.wind_speed.length),
        wind_direction: Math.round(data.wind_direction.reduce((a, b) => a + b) / data.wind_direction.length),
        condition: condition.description || '',
        condition_code: condition.id || 0,
        icon: condition.icon || '',
        precipitation_probability: Math.max(...data.precipitation) * 100,
        precipitation_amount: 0 // Would need additional API call for this
      });
    }
    
    return forecasts;
  }

  /**
   * Make HTTP request to weather API
   * 
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async makeRequest(url, options = {}) {
    const { params = {} } = options;
    
    const searchParams = new URLSearchParams(params);
    const fullUrl = `${url}?${searchParams.toString()}`;
    
    let lastError;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(fullUrl, {
          method: 'GET',
          headers: {
            'User-Agent': 'Lifeboard/1.0'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
        
      } catch (error) {
        lastError = error;
        
        if (this.logger) {
          await this.logger.warn(`Weather API: Request failed (attempt ${attempt}/${this.maxRetries})`, error);
        }
        
        if (attempt < this.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        }
      }
    }
    
    throw lastError;
  }
}

module.exports = WeatherService;