const { EventEmitter } = require('events');
const { factory: createLogger } = require('../core/logger/CoreLogger');
const path = require('path');
const fs = require('fs');

/**
 * Service Manager
 * 
 * Manages the lifecycle of all application services.
 * Replaces the plugin manager with a simpler, more direct approach.
 */
class ServiceManager extends EventEmitter {
  /**
   * Creates a new ServiceManager instance
   * 
   * @param {Object} lifeboardApp - Reference to the main application
   * @param {Object} options - Configuration options
   * @param {string} options.logDir - Directory for service logs
   * @param {Object} options.database - Database connection instance
   */
  constructor(lifeboardApp, options = {}) {
    super();
    
    this.app = lifeboardApp;
    this.services = new Map();
    this.serviceOrder = [];
    this.isInitialized = false;
    this.isStarted = false;
    
    // Initialize logger
    if (options.logDir) {
      const { CoreLogger } = require('../core/logger/CoreLogger');
      this.logger = new CoreLogger({
        component: 'service-manager',
        logDir: options.logDir,
        setupProcessHandlers: false
      });
    } else {
      this.logger = createLogger('service-manager');
    }
    
    // Store dependencies
    this.database = options.database;
    this.logDir = options.logDir;
    
    // Service registry
    this.serviceRegistry = {
      limitless: {
        name: 'Limitless Service',
        description: 'Limitless AI lifelog integration',
        path: './services/limitless-service.js',
        dependencies: [],
        enabled: true
      },
      weather: {
        name: 'Weather Service',
        description: 'Weather forecast integration',
        path: './services/weather-service.js',
        dependencies: [],
        enabled: true
      },
      news: {
        name: 'News Service',
        description: 'News aggregation service',
        path: './services/news-service.js',
        dependencies: [],
        enabled: false // Will be enabled when implemented
      }
    };
    
    // Statistics
    this.stats = {
      totalServices: 0,
      runningServices: 0,
      failedServices: 0,
      lastActivity: null,
      startTime: null
    };
  }

  /**
   * Initialize the service manager
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.logger.info('ServiceManager: Initializing service manager');
      
      // Load available services
      await this.loadServices();
      
      // Initialize services in dependency order
      await this.initializeServices();
      
      this.isInitialized = true;
      this.stats.startTime = new Date();
      
      await this.logger.info('ServiceManager: Service manager initialized successfully', {
        totalServices: this.services.size,
        serviceNames: Array.from(this.services.keys())
      });
      
      this.emit('initialized');
      
    } catch (error) {
      await this.logger.error('ServiceManager: Failed to initialize service manager', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Start all services
   * 
   * @returns {Promise<void>}
   */
  async start() {
    if (!this.isInitialized) {
      throw new Error('ServiceManager: Cannot start - not initialized');
    }
    
    if (this.isStarted) {
      return;
    }

    try {
      await this.logger.info('ServiceManager: Starting all services');
      
      // Start services in dependency order
      for (const serviceName of this.serviceOrder) {
        const service = this.services.get(serviceName);
        if (service && service.config.enabled) {
          try {
            await service.start();
            this.stats.runningServices++;
            await this.logger.info(`ServiceManager: Started service ${serviceName}`);
          } catch (error) {
            this.stats.failedServices++;
            await this.logger.error(`ServiceManager: Failed to start service ${serviceName}`, error);
            // Continue with other services
          }
        }
      }
      
      this.isStarted = true;
      this.stats.lastActivity = new Date();
      
      await this.logger.info('ServiceManager: All services started', {
        runningServices: this.stats.runningServices,
        failedServices: this.stats.failedServices
      });
      
      this.emit('started');
      
    } catch (error) {
      await this.logger.error('ServiceManager: Failed to start services', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Stop all services
   * 
   * @returns {Promise<void>}
   */
  async stop() {
    if (!this.isStarted) {
      return;
    }

    try {
      await this.logger.info('ServiceManager: Stopping all services');
      
      // Stop services in reverse dependency order
      const reverseOrder = [...this.serviceOrder].reverse();
      for (const serviceName of reverseOrder) {
        const service = this.services.get(serviceName);
        if (service && service.isStarted) {
          try {
            await service.stop();
            this.stats.runningServices--;
            await this.logger.info(`ServiceManager: Stopped service ${serviceName}`);
          } catch (error) {
            await this.logger.error(`ServiceManager: Failed to stop service ${serviceName}`, error);
            // Continue with other services
          }
        }
      }
      
      this.isStarted = false;
      this.stats.lastActivity = new Date();
      
      await this.logger.info('ServiceManager: All services stopped');
      this.emit('stopped');
      
    } catch (error) {
      await this.logger.error('ServiceManager: Failed to stop services', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Cleanup all services
   * 
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      await this.logger.info('ServiceManager: Cleaning up all services');
      
      // Stop services if running
      if (this.isStarted) {
        await this.stop();
      }
      
      // Cleanup services
      for (const [serviceName, service] of this.services) {
        try {
          await service.cleanup();
          await this.logger.info(`ServiceManager: Cleaned up service ${serviceName}`);
        } catch (error) {
          await this.logger.error(`ServiceManager: Failed to cleanup service ${serviceName}`, error);
        }
      }
      
      // Clear service registry
      this.services.clear();
      this.serviceOrder = [];
      this.isInitialized = false;
      
      await this.logger.info('ServiceManager: Cleanup completed');
      this.emit('cleanup');
      
    } catch (error) {
      await this.logger.error('ServiceManager: Failed to cleanup', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Load services from the registry
   * 
   * @returns {Promise<void>}
   */
  async loadServices() {
    await this.logger.info('ServiceManager: Loading services from registry');
    
    for (const [serviceId, serviceConfig] of Object.entries(this.serviceRegistry)) {
      if (!serviceConfig.enabled) {
        await this.logger.info(`ServiceManager: Skipping disabled service ${serviceId}`);
        continue;
      }
      
      try {
        await this.loadService(serviceId, serviceConfig);
      } catch (error) {
        await this.logger.error(`ServiceManager: Failed to load service ${serviceId}`, error);
        this.stats.failedServices++;
        // Continue with other services
      }
    }
    
    // Build dependency order
    this.buildDependencyOrder();
    
    this.stats.totalServices = this.services.size;
    await this.logger.info(`ServiceManager: Loaded ${this.services.size} services`);
  }

  /**
   * Load a single service
   * 
   * @param {string} serviceId - Service identifier
   * @param {Object} serviceConfig - Service configuration
   * @returns {Promise<void>}
   */
  async loadService(serviceId, serviceConfig) {
    await this.logger.debug(`ServiceManager: Loading service ${serviceId}`);
    
    try {
      // Resolve service path
      const servicePath = path.resolve(__dirname, serviceConfig.path);
      
      // Check if service file exists
      if (!fs.existsSync(servicePath)) {
        await this.logger.warn(`ServiceManager: Service file not found: ${servicePath}`);
        return;
      }
      
      // Load service class
      const ServiceClass = require(servicePath);
      
      // Create service instance
      const service = new ServiceClass({
        logDir: this.logDir,
        database: this.database,
        config: {
          ...serviceConfig,
          serviceId,
          enabled: serviceConfig.enabled
        }
      });
      
      // Store service
      this.services.set(serviceId, service);
      
      // Set up service event handlers
      this.setupServiceEventHandlers(serviceId, service);
      
      await this.logger.info(`ServiceManager: Loaded service ${serviceId} (${serviceConfig.name})`);
      
    } catch (error) {
      await this.logger.error(`ServiceManager: Failed to load service ${serviceId}`, error);
      throw error;
    }
  }

  /**
   * Initialize all loaded services
   * 
   * @returns {Promise<void>}
   */
  async initializeServices() {
    await this.logger.info('ServiceManager: Initializing services');
    
    for (const serviceName of this.serviceOrder) {
      const service = this.services.get(serviceName);
      if (service) {
        try {
          await service.initialize();
          await this.logger.info(`ServiceManager: Initialized service ${serviceName}`);
        } catch (error) {
          this.stats.failedServices++;
          await this.logger.error(`ServiceManager: Failed to initialize service ${serviceName}`, error);
          // Continue with other services
        }
      }
    }
    
    await this.logger.info('ServiceManager: Service initialization completed');
  }

  /**
   * Build dependency order for services
   *
   * @returns {void}
   */
  buildDependencyOrder() {
    // For now, simple order based on registry order
    // TODO: Implement proper dependency resolution
    this.serviceOrder = Array.from(this.services.keys());
    
    this.logger.debug('ServiceManager: Built service dependency order', {
      order: this.serviceOrder
    });
  }

  /**
   * Set up event handlers for a service
   * 
   * @param {string} serviceId - Service identifier
   * @param {Object} service - Service instance
   * @returns {void}
   */
  setupServiceEventHandlers(serviceId, service) {
    service.on('initialized', () => {
      this.emit('serviceInitialized', { serviceId, service });
    });
    
    service.on('started', () => {
      this.emit('serviceStarted', { serviceId, service });
    });
    
    service.on('stopped', () => {
      this.emit('serviceStopped', { serviceId, service });
    });
    
    service.on('error', (error) => {
      this.emit('serviceError', { serviceId, service, error });
    });
    
    service.on('taskCompleted', (data) => {
      this.stats.lastActivity = new Date();
      this.emit('serviceTaskCompleted', { serviceId, service, ...data });
    });
    
    service.on('taskError', (data) => {
      this.stats.lastActivity = new Date();
      this.emit('serviceTaskError', { serviceId, service, ...data });
    });
  }

  /**
   * Get a service by ID
   * 
   * @param {string} serviceId - Service identifier
   * @returns {Object|null} Service instance or null
   */
  getService(serviceId) {
    return this.services.get(serviceId);
  }

  /**
   * Get all services
   * 
   * @returns {Map} Map of service ID to service instance
   */
  getServices() {
    return this.services;
  }

  /**
   * Get service status
   * 
   * @param {string} serviceId - Service identifier
   * @returns {Object|null} Service status or null
   */
  getServiceStatus(serviceId) {
    const service = this.services.get(serviceId);
    return service ? service.getStatus() : null;
  }

  /**
   * Get all service statuses
   * 
   * @returns {Object} Map of service statuses
   */
  getAllServiceStatuses() {
    const statuses = {};
    for (const [serviceId, service] of this.services) {
      statuses[serviceId] = service.getStatus();
    }
    return statuses;
  }

  /**
   * Get service manager statistics
   * 
   * @returns {Object} Service manager statistics
   */
  getStats() {
    return {
      ...this.stats,
      services: this.getAllServiceStatuses(),
      serviceOrder: this.serviceOrder,
      uptime: this.stats.startTime ? Date.now() - this.stats.startTime.getTime() : 0
    };
  }

  /**
   * Enable a service
   * 
   * @param {string} serviceId - Service identifier
   * @returns {Promise<boolean>} Whether the operation was successful
   */
  async enableService(serviceId) {
    try {
      const service = this.services.get(serviceId);
      if (!service) {
        await this.logger.warn(`ServiceManager: Service ${serviceId} not found`);
        return false;
      }
      
      if (!service.isStarted) {
        await service.start();
        this.stats.runningServices++;
        await this.logger.info(`ServiceManager: Enabled service ${serviceId}`);
        this.emit('serviceEnabled', { serviceId, service });
        return true;
      }
      
      return true;
    } catch (error) {
      await this.logger.error(`ServiceManager: Failed to enable service ${serviceId}`, error);
      return false;
    }
  }

  /**
   * Disable a service
   * 
   * @param {string} serviceId - Service identifier
   * @returns {Promise<boolean>} Whether the operation was successful
   */
  async disableService(serviceId) {
    try {
      const service = this.services.get(serviceId);
      if (!service) {
        await this.logger.warn(`ServiceManager: Service ${serviceId} not found`);
        return false;
      }
      
      if (service.isStarted) {
        await service.stop();
        this.stats.runningServices--;
        await this.logger.info(`ServiceManager: Disabled service ${serviceId}`);
        this.emit('serviceDisabled', { serviceId, service });
        return true;
      }
      
      return true;
    } catch (error) {
      await this.logger.error(`ServiceManager: Failed to disable service ${serviceId}`, error);
      return false;
    }
  }

  /**
   * Restart a service
   * 
   * @param {string} serviceId - Service identifier
   * @returns {Promise<boolean>} Whether the operation was successful
   */
  async restartService(serviceId) {
    try {
      const service = this.services.get(serviceId);
      if (!service) {
        await this.logger.warn(`ServiceManager: Service ${serviceId} not found`);
        return false;
      }
      
      if (service.isStarted) {
        await service.stop();
        this.stats.runningServices--;
      }
      
      await service.start();
      this.stats.runningServices++;
      
      await this.logger.info(`ServiceManager: Restarted service ${serviceId}`);
      this.emit('serviceRestarted', { serviceId, service });
      return true;
      
    } catch (error) {
      await this.logger.error(`ServiceManager: Failed to restart service ${serviceId}`, error);
      return false;
    }
  }

  /**
   * List all available services
   * 
   * @returns {Array} List of service information
   */
  listServices() {
    const serviceList = [];
    for (const [serviceId, service] of this.services) {
      const status = service.getStatus();
      serviceList.push({
        id: serviceId,
        name: this.serviceRegistry[serviceId]?.name || serviceId,
        description: this.serviceRegistry[serviceId]?.description || '',
        status: status.status,
        isRunning: status.isStarted,
        uptime: status.uptime,
        lastActivity: status.lastActivity,
        errors: status.errors
      });
    }
    return serviceList;
  }
}

module.exports = { ServiceManager };