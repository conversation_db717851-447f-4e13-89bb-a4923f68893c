/**
 * Base Module Class
 *
 * Abstract base class that all Lifeboard modules must extend.
 * Provides standard lifecycle methods, logging, and integration patterns.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * Base class for all Lifeboard modules.
 *
 * Provides standard lifecycle methods and common functionality.
 * All modules must extend this class and implement required methods.
 *
 * @abstract
 */
class BaseModule {
  /**
   * Create a new module instance.
   *
   * @param {Object} options - Module configuration options
   * @param {string} options.name - Module name
   * @param {string} options.version - Module version
   * @param {Object} options.config - Module configuration from environment
   * @param {Object} options.services - Available services (logger, database, etc.)
   */
  constructor(options = {}) {
    if (this.constructor === BaseModule) {
      throw new Error('BaseModule is abstract and cannot be instantiated directly');
    }

    // Validate required options
    if (!options.name || typeof options.name !== 'string') {
      throw new Error('Module name is required and must be a string');
    }

    if (!options.version || typeof options.version !== 'string') {
      throw new Error('Module version is required and must be a string');
    }

    // Module metadata
    this.name = options.name;
    this.version = options.version;
    this.description = options.description || '';
    this.author = options.author || 'Lifeboard Team';

    // Module state
    this.isInitialized = false;
    this.isStarted = false;
    this.isEnabled = true;

    // Configuration and services
    this.config = options.config || {};
    this.services = options.services || {};

    // Initialize logger
    this.logger = options.logger || createLogger(`module:${this.name}`);

    // Scheduled tasks
    this.scheduledTasks = new Map();

    // Module lifecycle timestamps
    this.timestamps = {
      created: new Date(),
      initialized: null,
      started: null,
      stopped: null
    };

    this.logger.debug('Module instance created', {
      name: this.name,
      version: this.version,
      hasConfig: Object.keys(this.config).length > 0
    });
  }

  /**
   * Get module metadata.
   *
   * @returns {Object} Module metadata
   */
  getMetadata() {
    return {
      name: this.name,
      version: this.version,
      description: this.description,
      author: this.author,
      isInitialized: this.isInitialized,
      isStarted: this.isStarted,
      isEnabled: this.isEnabled,
      timestamps: { ...this.timestamps }
    };
  }

  /**
   * Get module status.
   *
   * @returns {Object} Current module status
   */
  getStatus() {
    return {
      name: this.name,
      state: this.getState(),
      isHealthy: this.isHealthy(),
      scheduledTasks: this.scheduledTasks.size,
      uptime: this.getUptime(),
      lastActivity: this.getLastActivity()
    };
  }

  /**
   * Get current module state.
   *
   * @returns {string} Current state
   */
  getState() {
    if (!this.isEnabled) return 'disabled';
    if (!this.isInitialized) return 'created';
    if (!this.isStarted) return 'initialized';
    return 'running';
  }

  /**
   * Check if module is healthy.
   *
   * Override this method to implement custom health checks.
   *
   * @returns {boolean} True if module is healthy
   */
  isHealthy() {
    return this.isEnabled && this.isInitialized && this.isStarted;
  }

  /**
   * Get module uptime in milliseconds.
   *
   * @returns {number|null} Uptime in milliseconds, null if not started
   */
  getUptime() {
    if (!this.timestamps.started) return null;
    return Date.now() - this.timestamps.started.getTime();
  }

  /**
   * Get last activity timestamp.
   *
   * Override this method to track module-specific activity.
   *
   * @returns {Date|null} Last activity timestamp
   */
  getLastActivity() {
    return this.timestamps.started;
  }

  /**
   * Initialize the module.
   *
   * This method is called once when the module is loaded.
   * Override this method to implement module-specific initialization.
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      this.logger.warn('Module already initialized', { name: this.name });
      return;
    }

    this.logger.info('Initializing module', { name: this.name });

    try {
      // Call module-specific initialization
      await this.onInitialize();

      this.isInitialized = true;
      this.timestamps.initialized = new Date();

      this.logger.info('Module initialized successfully', { name: this.name });
    } catch (error) {
      this.logger.error('Module initialization failed', {
        name: this.name,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Start the module.
   *
   * This method is called to start the module after initialization.
   * Override this method to implement module-specific startup logic.
   *
   * @returns {Promise<void>}
   */
  async start() {
    if (!this.isInitialized) {
      throw new Error('Module must be initialized before starting');
    }

    if (this.isStarted) {
      this.logger.warn('Module already started', { name: this.name });
      return;
    }

    this.logger.info('Starting module', { name: this.name });

    try {
      // Call module-specific startup
      await this.onStart();

      // Start all scheduled tasks
      await this.startAllScheduledTasks();

      this.isStarted = true;
      this.timestamps.started = new Date();

      this.logger.info('Module started successfully', { name: this.name });
    } catch (error) {
      this.logger.error('Module startup failed', {
        name: this.name,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Stop the module.
   *
   * This method is called to gracefully stop the module.
   * Override this method to implement module-specific cleanup.
   *
   * @returns {Promise<void>}
   */
  async stop() {
    if (!this.isStarted) {
      this.logger.warn('Module not started', { name: this.name });
      return;
    }

    this.logger.info('Stopping module', { name: this.name });

    try {
      // Clear all scheduled tasks
      await this.clearAllScheduledTasks();

      // Call module-specific cleanup
      await this.onStop();

      this.isStarted = false;
      this.timestamps.stopped = new Date();

      this.logger.info('Module stopped successfully', { name: this.name });
    } catch (error) {
      this.logger.error('Module stop failed', {
        name: this.name,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Enable the module.
   *
   * @returns {Promise<void>}
   */
  async enable() {
    if (this.isEnabled) {
      this.logger.warn('Module already enabled', { name: this.name });
      return;
    }

    this.logger.info('Enabling module', { name: this.name });
    this.isEnabled = true;

    if (this.isInitialized && !this.isStarted) {
      await this.start();
    }
  }

  /**
   * Disable the module.
   *
   * @returns {Promise<void>}
   */
  async disable() {
    if (!this.isEnabled) {
      this.logger.warn('Module already disabled', { name: this.name });
      return;
    }

    this.logger.info('Disabling module', { name: this.name });

    if (this.isStarted) {
      await this.stop();
    }

    this.isEnabled = false;
  }

  /**
   * Schedule a recurring task.
   *
   * @param {string} taskId - Unique task identifier
   * @param {Function} taskFunction - Function to execute
   * @param {number} intervalMs - Interval in milliseconds
   * @param {Object} options - Task options
   * @returns {string} Task ID
   */
  scheduleTask(taskId, taskFunction, intervalMs, options = {}) {
    if (this.scheduledTasks.has(taskId)) {
      throw new Error(`Task with ID '${taskId}' already exists`);
    }

    const task = {
      id: taskId,
      function: taskFunction,
      interval: intervalMs,
      options: options,
      intervalId: null,
      lastRun: null,
      runCount: 0,
      errorCount: 0
    };

    // Start the task if module is running
    if (this.isStarted) {
      task.intervalId = setInterval(async () => {
        try {
          await taskFunction();
          task.lastRun = new Date();
          task.runCount++;
        } catch (error) {
          task.errorCount++;
          this.logger.error('Scheduled task failed', {
            module: this.name,
            taskId: taskId,
            error: error.message
          });
        }
      }, intervalMs);
    }

    this.scheduledTasks.set(taskId, task);

    this.logger.debug('Scheduled task created', {
      module: this.name,
      taskId: taskId,
      intervalMs: intervalMs
    });

    return taskId;
  }

  /**
   * Clear a scheduled task.
   *
   * @param {string} taskId - Task identifier
   * @returns {boolean} True if task was cleared
   */
  clearScheduledTask(taskId) {
    const task = this.scheduledTasks.get(taskId);
    if (!task) {
      return false;
    }

    if (task.intervalId) {
      clearInterval(task.intervalId);
    }

    this.scheduledTasks.delete(taskId);

    this.logger.debug('Scheduled task cleared', {
      module: this.name,
      taskId: taskId
    });

    return true;
  }

  /**
   * Clear all scheduled tasks.
   *
   * @returns {Promise<void>}
   */
  async clearAllScheduledTasks() {
    const taskIds = Array.from(this.scheduledTasks.keys());

    for (const taskId of taskIds) {
      this.clearScheduledTask(taskId);
    }

    this.logger.debug('All scheduled tasks cleared', {
      module: this.name,
      clearedCount: taskIds.length
    });
  }

  /**
   * Start all scheduled tasks.
   *
   * @private
   * @returns {Promise<void>}
   */
  async startAllScheduledTasks() {
    for (const [taskId, task] of this.scheduledTasks) {
      if (!task.intervalId) {
        task.intervalId = setInterval(async () => {
          try {
            await task.function();
            task.lastRun = new Date();
            task.runCount++;
          } catch (error) {
            task.errorCount++;
            this.logger.error('Scheduled task failed', {
              module: this.name,
              taskId: taskId,
              error: error.message
            });
          }
        }, task.interval);

        this.logger.debug('Started scheduled task', {
          module: this.name,
          taskId: taskId
        });
      }
    }
  }

  // Abstract methods that modules must implement

  /**
   * Module-specific initialization logic.
   *
   * Override this method to implement custom initialization.
   *
   * @abstract
   * @returns {Promise<void>}
   */
  async onInitialize() {
    // Default implementation - modules can override
  }

  /**
   * Module-specific startup logic.
   *
   * Override this method to implement custom startup behavior.
   *
   * @abstract
   * @returns {Promise<void>}
   */
  async onStart() {
    // Default implementation - modules can override
  }

  /**
   * Module-specific cleanup logic.
   *
   * Override this method to implement custom cleanup behavior.
   *
   * @abstract
   * @returns {Promise<void>}
   */
  async onStop() {
    // Default implementation - modules can override
  }
}

module.exports = BaseModule;
