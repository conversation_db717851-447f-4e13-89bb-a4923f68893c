/**
 * Module Manager
 *
 * Manages the lifecycle of Lifeboard modules, replacing the plugin system
 * with a more integrated class-based approach.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

const fs = require('fs');
const path = require('path');
const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * Manages module loading, lifecycle, and integration.
 *
 * Replaces the plugin system with direct class instantiation and
 * integration with the service manager.
 */
class ModuleManager {
  /**
   * Create a new ModuleManager instance.
   *
   * @param {Object} options - Configuration options
   * @param {Object} options.logger - Logger instance
   * @param {string} options.modulesDir - Directory containing modules
   * @param {Object} options.services - Available services
   * @param {Object} options.config - Environment configuration
   */
  constructor(options = {}) {
    this.logger = options.logger || createLogger('ModuleManager');
    this.modulesDir = options.modulesDir || path.join(__dirname, '../../modules');
    this.services = options.services || {};
    this.config = options.config || {};

    // Module registry
    this.modules = new Map();
    this.moduleClasses = new Map();

    // State
    this.isInitialized = false;
    this.isStarted = false;

    this.logger.info('ModuleManager created', {
      modulesDir: this.modulesDir,
      servicesCount: Object.keys(this.services).length
    });
  }

  /**
   * Initialize the module manager.
   *
   * Discovers and loads all available modules.
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      this.logger.warn('ModuleManager already initialized');
      return;
    }

    this.logger.info('Initializing ModuleManager');

    try {
      // Discover available modules
      await this.discoverModules();

      // Load module classes
      await this.loadModuleClasses();

      // Create module instances
      await this.createModuleInstances();

      // Initialize all modules
      await this.initializeModules();

      this.isInitialized = true;
      this.logger.info('ModuleManager initialized successfully', {
        moduleCount: this.modules.size
      });
    } catch (error) {
      this.logger.error('ModuleManager initialization failed', {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Start all modules.
   *
   * @returns {Promise<void>}
   */
  async start() {
    if (!this.isInitialized) {
      throw new Error('ModuleManager must be initialized before starting');
    }

    if (this.isStarted) {
      this.logger.warn('ModuleManager already started');
      return;
    }

    this.logger.info('Starting all modules');

    const startPromises = Array.from(this.modules.values()).map(async (module) => {
      try {
        if (module.isEnabled) {
          await module.start();
          this.logger.debug('Module started', { name: module.name });
        } else {
          this.logger.debug('Module disabled, skipping start', { name: module.name });
        }
      } catch (error) {
        this.logger.error('Failed to start module', {
          name: module.name,
          error: error.message
        });
        // Continue with other modules
      }
    });

    await Promise.allSettled(startPromises);

    this.isStarted = true;
    this.logger.info('All modules started');
  }

  /**
   * Stop all modules.
   *
   * @returns {Promise<void>}
   */
  async stop() {
    if (!this.isStarted) {
      this.logger.warn('ModuleManager not started');
      return;
    }

    this.logger.info('Stopping all modules');

    const stopPromises = Array.from(this.modules.values()).map(async (module) => {
      try {
        if (module.isStarted) {
          await module.stop();
          this.logger.debug('Module stopped', { name: module.name });
        }
      } catch (error) {
        this.logger.error('Failed to stop module', {
          name: module.name,
          error: error.message
        });
        // Continue with other modules
      }
    });

    await Promise.allSettled(stopPromises);

    this.isStarted = false;
    this.logger.info('All modules stopped');
  }

  /**
   * Get a module by name.
   *
   * @param {string} name - Module name
   * @returns {Object|null} Module instance or null if not found
   */
  getModule(name) {
    return this.modules.get(name) || null;
  }

  /**
   * Get all modules.
   *
   * @returns {Array} Array of module instances
   */
  getAllModules() {
    return Array.from(this.modules.values());
  }

  /**
   * Get module status for all modules.
   *
   * @returns {Array} Array of module status objects
   */
  getModuleStatuses() {
    return Array.from(this.modules.values()).map(module => module.getStatus());
  }

  /**
   * Enable a module.
   *
   * @param {string} name - Module name
   * @returns {Promise<boolean>} True if enabled successfully
   */
  async enableModule(name) {
    const module = this.modules.get(name);
    if (!module) {
      this.logger.error('Module not found', { name });
      return false;
    }

    try {
      await module.enable();
      this.logger.info('Module enabled', { name });
      return true;
    } catch (error) {
      this.logger.error('Failed to enable module', {
        name,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Disable a module.
   *
   * @param {string} name - Module name
   * @returns {Promise<boolean>} True if disabled successfully
   */
  async disableModule(name) {
    const module = this.modules.get(name);
    if (!module) {
      this.logger.error('Module not found', { name });
      return false;
    }

    try {
      await module.disable();
      this.logger.info('Module disabled', { name });
      return true;
    } catch (error) {
      this.logger.error('Failed to disable module', {
        name,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Discover available modules in the modules directory.
   *
   * @private
   * @returns {Promise<void>}
   */
  async discoverModules() {
    this.logger.debug('Discovering modules', { modulesDir: this.modulesDir });

    if (!fs.existsSync(this.modulesDir)) {
      this.logger.warn('Modules directory does not exist', { modulesDir: this.modulesDir });
      return;
    }

    const entries = fs.readdirSync(this.modulesDir, { withFileTypes: true });
    const moduleDirectories = entries
      .filter(entry => entry.isDirectory())
      .map(entry => entry.name);

    this.logger.debug('Found module directories', {
      directories: moduleDirectories,
      count: moduleDirectories.length
    });

    // Store discovered modules for loading
    this.discoveredModules = moduleDirectories;
  }

  /**
   * Load module classes from discovered modules.
   *
   * @private
   * @returns {Promise<void>}
   */
  async loadModuleClasses() {
    this.logger.debug('Loading module classes');

    for (const moduleName of this.discoveredModules || []) {
      try {
        const modulePath = path.join(this.modulesDir, moduleName, 'index.js');
        
        if (!fs.existsSync(modulePath)) {
          this.logger.warn('Module index.js not found', {
            moduleName,
            expectedPath: modulePath
          });
          continue;
        }

        // Require the module class
        const ModuleClass = require(modulePath);
        
        if (typeof ModuleClass !== 'function') {
          this.logger.error('Module does not export a class', { moduleName });
          continue;
        }

        this.moduleClasses.set(moduleName, ModuleClass);
        this.logger.debug('Module class loaded', { moduleName });

      } catch (error) {
        this.logger.error('Failed to load module class', {
          moduleName,
          error: error.message,
          stack: error.stack
        });
      }
    }

    this.logger.info('Module classes loaded', {
      loadedCount: this.moduleClasses.size,
      totalDiscovered: this.discoveredModules?.length || 0
    });
  }

  /**
   * Create instances of all loaded module classes.
   *
   * @private
   * @returns {Promise<void>}
   */
  async createModuleInstances() {
    this.logger.debug('Creating module instances');

    for (const [moduleName, ModuleClass] of this.moduleClasses) {
      try {
        // Get module-specific configuration
        const moduleConfig = this.getModuleConfig(moduleName);
        
        // Check if module is enabled
        const isEnabled = this.isModuleEnabled(moduleName);

        // Create module instance
        const moduleInstance = new ModuleClass({
          name: moduleName,
          version: '1.0.0', // TODO: Get from module metadata
          config: moduleConfig,
          services: this.services,
          logger: createLogger(`module:${moduleName}`)
        });

        // Set enabled state
        moduleInstance.isEnabled = isEnabled;

        this.modules.set(moduleName, moduleInstance);
        this.logger.debug('Module instance created', {
          moduleName,
          isEnabled
        });

      } catch (error) {
        this.logger.error('Failed to create module instance', {
          moduleName,
          error: error.message,
          stack: error.stack
        });
      }
    }

    this.logger.info('Module instances created', {
      instanceCount: this.modules.size
    });
  }

  /**
   * Initialize all module instances.
   *
   * @private
   * @returns {Promise<void>}
   */
  async initializeModules() {
    this.logger.debug('Initializing modules');

    const initPromises = Array.from(this.modules.values()).map(async (module) => {
      try {
        await module.initialize();
        this.logger.debug('Module initialized', { name: module.name });
      } catch (error) {
        this.logger.error('Failed to initialize module', {
          name: module.name,
          error: error.message
        });
        // Continue with other modules
      }
    });

    await Promise.allSettled(initPromises);
    this.logger.info('Module initialization completed');
  }

  /**
   * Get configuration for a specific module.
   *
   * @private
   * @param {string} moduleName - Module name
   * @returns {Object} Module configuration
   */
  getModuleConfig(moduleName) {
    // Extract module-specific config from environment
    const moduleConfig = {};
    const prefix = `${moduleName.toUpperCase()}_`;

    for (const [key, value] of Object.entries(this.config)) {
      if (key.startsWith(prefix)) {
        const configKey = key.substring(prefix.length).toLowerCase();
        moduleConfig[configKey] = value;
      }
    }

    return moduleConfig;
  }

  /**
   * Check if a module is enabled via environment configuration.
   *
   * @private
   * @param {string} moduleName - Module name
   * @returns {boolean} True if module is enabled
   */
  isModuleEnabled(moduleName) {
    const enabledKey = `${moduleName.toUpperCase()}_ENABLED`;
    return this.config[enabledKey] === 'true';
  }
}

module.exports = ModuleManager;
