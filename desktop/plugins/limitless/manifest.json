{"author": "Lifeboard Team", "description": "Limitless AI lifelog integration with automatic 4-hour sync and cursor-based pagination", "homepage": "https://lifeboard.app/plugins/limitless", "id": "limitless", "main": "main.js", "minAppVersion": "0.1.0", "name": "Limit<PERSON> Plugin", "permissions": ["network", "storage"], "settings": {"apiKey": {"description": "Limitless AI API Key", "sensitive": true, "type": "string"}, "enabled": {"default": false, "description": "Enable automatic lifelog synchronization", "type": "boolean"}, "syncInterval": {"default": 4, "description": "Sync interval in hours (default: 4 hours)", "type": "number"}}, "version": "1.0.0"}