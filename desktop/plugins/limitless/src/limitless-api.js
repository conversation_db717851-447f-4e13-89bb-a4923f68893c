/**
 * Limitless API Integration
 *
 * Handles all interactions with the Limitless AI API.
 * Manages authentication, cursor-based pagination, and response processing.
 *
 * @module LimitlessAPI
 */

/**
 * Limitless API class for interacting with the Limitless AI service
 *
 * @class LimitlessAPI
 */
class LimitlessAPI {
  /**
   * Creates a new LimitlessAPI instance
   *
   * @param {Object} api - The plugin API instance
   * @param {Object} logger - The logger instance
   */
  constructor(api, logger) {
    this.api = api;
    this.logger = logger;
    this.baseUrl = 'https://api.limitless.ai';
    this.rateLimitDelay = 1000; // 1 second between requests
    this.maxRetries = 3;
    this.retryDelay = 2000; // 2 seconds initial retry delay
  }

  /**
   * Creates a fetch wrapper to provide requestUrl-like interface
   *
   * @private
   * @param {string} url - The request URL
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response object with status, headers, json(), text() methods
   */
  async createFetchWrapper(url, options = {}) {
    const response = await this.api.network.fetch(url, options);
    
    return {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: response.headers,
      json: () => response.json(),
      text: () => response.text()
    };
  }

  /**
   * Validates the API key by making a test request
   *
   * @param {string} apiKey - The API key to validate
   * @returns {Promise<boolean>} Whether the API key is valid
   */
  async validateAPIKey(apiKey) {
    try {
      if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
        return false;
      }

      const url = `${this.baseUrl}/v1/lifelogs?limit=1`;
      
      const response = await this.createFetchWrapper(url, {
        method: 'GET',
        headers: {
          'X-API-Key': apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        await this.logger.debug('API key validation successful');
        return true;
      } else {
        await this.logger.debug('API key validation failed', {
          status: response.status,
          statusText: response.statusText
        });
        return false;
      }
    } catch (error) {
      await this.logger.error('API key validation error:', error);
      return false;
    }
  }

  /**
   * Fetches lifelogs with cursor-based pagination
   *
   * @param {Object} options - Fetch options
   * @param {string} options.apiKey - The API key
   * @param {string} [options.cursor] - Pagination cursor
   * @param {number} [options.limit=10] - Number of records to fetch
   * @param {string} [options.timezone='UTC'] - Timezone for date operations
   * @param {string} [options.startDate] - Start date filter (YYYY-MM-DD)
   * @param {string} [options.endDate] - End date filter (YYYY-MM-DD)
   * @param {boolean} [options.includeMarkdown=true] - Include markdown content
   * @param {boolean} [options.includeHeadings=true] - Include headings
   * @returns {Promise<Object>} Fetch result with lifelogs and pagination info
   */
  async fetchLifelogs(options = {}) {
    const {
      apiKey,
      cursor,
      limit = 10,
      timezone = 'UTC',
      startDate,
      endDate,
      includeMarkdown = true,
      includeHeadings = true
    } = options;

    let retryCount = 0;
    
    while (retryCount <= this.maxRetries) {
      try {
        await this.logger.debug('Fetching lifelogs', {
          cursor: cursor ? '[PRESENT]' : '[NONE]',
          limit,
          timezone,
          startDate,
          endDate,
          includeMarkdown,
          includeHeadings,
          attempt: retryCount + 1
        });

        if (!apiKey) {
          throw new Error('API key is required for fetching lifelogs');
        }

        // Build query parameters
        const searchParams = new URLSearchParams({
          limit: limit.toString(),
          includeMarkdown: includeMarkdown.toString(),
          includeHeadings: includeHeadings.toString()
        });

        if (cursor) {
          searchParams.append('cursor', cursor);
        }

        if (timezone) {
          searchParams.append('timezone', timezone);
        }

        if (startDate) {
          searchParams.append('start', startDate);
        }

        if (endDate) {
          searchParams.append('end', endDate);
        }

        const url = `${this.baseUrl}/v1/lifelogs?${searchParams.toString()}`;

        const response = await this.createFetchWrapper(url, {
          method: 'GET',
          headers: {
            'X-API-Key': apiKey,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Failed to fetch lifelogs: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();

        await this.logger.debug('Lifelogs fetched successfully', {
          recordCount: data.data?.lifelogs?.length || 0,
          hasNextCursor: !!data.meta?.lifelogs?.nextCursor,
          totalCount: data.meta?.lifelogs?.count || 0
        });

        // Apply rate limiting
        if (data.meta?.lifelogs?.nextCursor) {
          await this.sleep(this.rateLimitDelay);
        }

        return {
          success: true,
          data: data.data?.lifelogs || [],
          meta: data.meta?.lifelogs || {},
          nextCursor: data.meta?.lifelogs?.nextCursor || null
        };

      } catch (error) {
        retryCount++;
        
        if (retryCount <= this.maxRetries && this.shouldRetry(error)) {
          const delay = this.retryDelay * Math.pow(2, retryCount - 1); // Exponential backoff
          await this.logger.warn(`Fetch failed, retrying in ${delay}ms (attempt ${retryCount}/${this.maxRetries})`, error);
          await this.sleep(delay);
        } else {
          await this.logger.error('Lifelog fetch failed after retries', error);
          return {
            success: false,
            error: error.message,
            data: [],
            meta: {},
            nextCursor: null
          };
        }
      }
    }
  }

  /**
   * Determines if a request should be retried based on the error
   *
   * @private
   * @param {Error} error - The error that occurred
   * @returns {boolean} Whether to retry the request
   */
  shouldRetry(error) {
    const retryableMessages = [
      'network error',
      'timeout',
      'connection refused',
      'dns lookup failed',
      'rate limit',
      'service unavailable'
    ];

    const errorMessage = error.message.toLowerCase();
    return retryableMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * Sleep utility for rate limiting and retries
   *
   * @private
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Gets API usage statistics
   *
   * @returns {Object} API usage statistics
   */
  getApiStats() {
    return {
      baseUrl: this.baseUrl,
      rateLimitDelay: this.rateLimitDelay,
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay
    };
  }
}

module.exports = LimitlessAPI;