/**
 * Sync Manager for Limitless Plugin
 *
 * Handles scheduling and execution of lifelog data synchronization.
 * Manages 4-hour background sync with cursor-based pagination.
 *
 * @module SyncManager
 */

/**
 * Sync Manager class for handling lifelog synchronization
 *
 * @class SyncManager
 */
class SyncManager {
  /**
   * Creates a new SyncManager instance
   *
   * @param {Object} api - The plugin API instance
   * @param {Object} logger - The logger instance
   * @param {Object} limitlessAPI - The LimitlessAPI instance
   * @param {Object} dataProcessor - The DataProcessor instance
   */
  constructor(api, logger, limitlessAPI, dataProcessor) {
    this.api = api;
    this.logger = logger;
    this.limitlessAPI = limitlessAPI;
    this.dataProcessor = dataProcessor;

    // Sync state
    this.syncInterval = null;
    this.lastSyncTime = null;
    this.nextSyncTime = null;
    this.isSyncing = false;
    this.syncStatus = 'idle'; // 'idle' | 'syncing' | 'error'
    this.syncError = null;
    this.processedLifelogIds = new Set(); // Track processed IDs within sync session

    // Default sync interval (4 hours in milliseconds)
    this.defaultSyncInterval = 4 * 60 * 60 * 1000;
  }

  /**
   * Initializes the sync manager
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Load sync state from settings
      await this.loadSyncState();

      // Start auto-sync if enabled
      const settings = await this.api.settings.get();
      if (settings?.enabled === true) {
        await this.startAutoSync();
      }

      await this.logger.debug('Sync manager initialized', {
        enabled: settings?.enabled === true,
        lastSyncTime: this.lastSyncTime
      });
    } catch (error) {
      await this.logger.error('Failed to initialize sync manager:', error);
      throw error;
    }
  }

  /**
   * Starts automatic synchronization based on the configured interval
   *
   * @returns {Promise<void>}
   */
  async startAutoSync() {
    try {
      // Clear any existing interval
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }

      // Get interval from settings or use default
      const settings = await this.api.settings.get();
      const syncIntervalHours = settings?.syncInterval || 4;
      const syncIntervalMs = syncIntervalHours * 60 * 60 * 1000;

      // Set up the interval
      this.syncInterval = setInterval(() => {
        this.performSync().catch(error => {
          this.logger.error('Scheduled sync failed:', error);
        });
      }, syncIntervalMs);

      // Update next sync time
      this.nextSyncTime = new Date(Date.now() + syncIntervalMs);

      // Perform initial sync if needed
      const timeSinceLastSync = this.lastSyncTime ? 
        Date.now() - new Date(this.lastSyncTime).getTime() : 
        syncIntervalMs + 1;

      if (timeSinceLastSync > syncIntervalMs) {
        setTimeout(() => {
          this.performSync().catch(error => {
            this.logger.error('Initial sync failed:', error);
          });
        }, 1000); // Delay initial sync by 1 second
      }

      await this.logger.info(`Auto-sync started with ${syncIntervalHours} hour interval`);
    } catch (error) {
      await this.logger.error('Failed to start auto-sync:', error);
      throw error;
    }
  }

  /**
   * Stops automatic synchronization
   *
   * @returns {Promise<void>}
   */
  async stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      this.nextSyncTime = null;
      await this.logger.info('Auto-sync stopped');
    }
  }

  /**
   * Performs a full synchronization with cursor-based pagination
   *
   * @param {Object} [options] - Sync options
   * @param {boolean} [options.force=false] - Whether to force sync even if recently updated
   * @returns {Promise<Object>} Sync result
   */
  async performSync(options = {}) {
    const { force = false } = options;

    // Check if already syncing
    if (this.isSyncing) {
      await this.logger.debug('Sync already in progress, skipping');
      return { success: false, message: 'Sync already in progress' };
    }

    // Check if plugin is enabled
    const settings = await this.api.settings.get();
    if (!settings?.enabled) {
      await this.logger.debug('Plugin not enabled, skipping sync');
      return { success: false, message: 'Plugin not enabled' };
    }

    // Check if we have an API key
    if (!settings?.apiKey) {
      await this.logger.debug('API key not configured, skipping sync');
      return { success: false, message: 'API key not configured' };
    }

    // Check cooldown period (unless forced)
    if (!force && this.lastSyncTime) {
      const timeSinceLastSync = Date.now() - new Date(this.lastSyncTime).getTime();
      const minSyncInterval = 5 * 60 * 1000; // 5 minutes minimum

      if (timeSinceLastSync < minSyncInterval) {
        await this.logger.debug('Skipping sync, too soon since last sync');
        return {
          success: false,
          message: 'Minimum sync interval not reached',
          nextSync: new Date(Date.now() + (minSyncInterval - timeSinceLastSync))
        };
      }
    }

    // Start sync
    this.isSyncing = true;
    this.syncStatus = 'syncing';
    this.syncError = null;
    this.processedLifelogIds.clear();

    const startTime = Date.now();
    let totalProcessed = 0;
    let cursor = null;
    const allLifelogs = [];

    try {
      await this.logger.info('Starting lifelog sync with cursor-based pagination');

      // Fetch all lifelogs using cursor-based pagination
      do {
        const fetchOptions = {
          apiKey: settings.apiKey,
          cursor,
          limit: 50, // Reasonable batch size
          timezone: 'UTC',
          includeMarkdown: true,
          includeHeadings: true
        };

        // Use incremental sync if we have a last sync time
        if (this.lastSyncTime && !force) {
          fetchOptions.startDate = this.lastSyncTime;
        }

        const result = await this.limitlessAPI.fetchLifelogs(fetchOptions);

        if (!result.success) {
          throw new Error(`Failed to fetch lifelogs: ${result.error}`);
        }

        // Filter out duplicates within this sync session
        const newLifelogs = result.data.filter(lifelog => {
          if (this.processedLifelogIds.has(lifelog.id)) {
            return false;
          }
          this.processedLifelogIds.add(lifelog.id);
          return true;
        });

        allLifelogs.push(...newLifelogs);
        totalProcessed += newLifelogs.length;
        cursor = result.nextCursor;

        await this.logger.debug('Fetched batch of lifelogs', {
          batchSize: result.data.length,
          newLifelogs: newLifelogs.length,
          totalProcessed,
          hasNextCursor: !!cursor
        });

        // Process in batches to avoid memory issues
        if (allLifelogs.length >= 100) {
          await this.dataProcessor.processLifelogs(allLifelogs.splice(0, 100));
        }

      } while (cursor);

      // Process remaining lifelogs
      if (allLifelogs.length > 0) {
        await this.dataProcessor.processLifelogs(allLifelogs);
      }

      // Update sync state
      this.lastSyncTime = new Date().toISOString();
      this.syncStatus = 'idle';

      // Update next sync time if we have an interval
      if (this.syncInterval) {
        const syncIntervalHours = settings?.syncInterval || 4;
        const syncIntervalMs = syncIntervalHours * 60 * 60 * 1000;
        this.nextSyncTime = new Date(Date.now() + syncIntervalMs);
      }

      // Save sync state
      await this.updateLastSyncTimestamp(this.lastSyncTime);

      const duration = Date.now() - startTime;

      await this.logger.info('Lifelog sync completed successfully', {
        totalProcessed,
        duration: `${duration}ms`,
        lastSync: this.lastSyncTime,
        nextSync: this.nextSyncTime
      });

      return {
        success: true,
        message: 'Sync completed successfully',
        totalProcessed,
        duration,
        lastSync: this.lastSyncTime,
        nextSync: this.nextSyncTime
      };

    } catch (error) {
      // Handle sync error
      this.syncStatus = 'error';
      this.syncError = error.message;

      const duration = Date.now() - startTime;

      await this.logger.error('Lifelog sync failed', error, {
        totalProcessed,
        duration: `${duration}ms`
      });

      return {
        success: false,
        message: 'Sync failed',
        error: error.message,
        totalProcessed,
        duration
      };
    } finally {
      this.isSyncing = false;
      this.processedLifelogIds.clear();
    }
  }

  /**
   * Gets the current sync status
   *
   * @returns {Object} Sync status information
   */
  getSyncStatus() {
    return {
      status: this.syncStatus,
      isSyncing: this.isSyncing,
      lastSync: this.lastSyncTime,
      nextSync: this.nextSyncTime,
      error: this.syncError
    };
  }

  /**
   * Gets the last sync time
   *
   * @returns {Date|null} Last sync time or null if never synced
   */
  getLastSyncTime() {
    return this.lastSyncTime ? new Date(this.lastSyncTime) : null;
  }

  /**
   * Gets the next scheduled sync time
   *
   * @returns {Date|null} Next sync time or null if not scheduled
   */
  getNextSyncTime() {
    return this.nextSyncTime ? new Date(this.nextSyncTime) : null;
  }

  /**
   * Loads sync state from settings
   *
   * @private
   * @returns {Promise<void>}
   */
  async loadSyncState() {
    try {
      const settings = await this.api.settings.get();
      this.lastSyncTime = settings?.lastSyncTime || null;
      
      if (this.lastSyncTime) {
        await this.logger.debug('Loaded last sync time from settings', {
          lastSyncTime: this.lastSyncTime
        });
      }
    } catch (error) {
      await this.logger.error('Failed to load sync state:', error);
      this.lastSyncTime = null;
    }
  }

  /**
   * Updates the last sync timestamp in settings
   *
   * @param {string} timestamp - ISO timestamp
   * @returns {Promise<void>}
   */
  async updateLastSyncTimestamp(timestamp) {
    try {
      const settings = await this.api.settings.get() || {};
      settings.lastSyncTime = timestamp;
      await this.api.settings.set(settings);
      
      await this.logger.debug('Updated last sync timestamp', {
        timestamp
      });
    } catch (error) {
      await this.logger.error('Failed to update last sync timestamp:', error);
    }
  }

  /**
   * Cleans up resources when the plugin is disabled
   *
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      // Stop any running syncs
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }

      // Clear processed IDs
      this.processedLifelogIds.clear();

      await this.logger.debug('Sync manager cleaned up');
    } catch (error) {
      await this.logger.error('Error during sync manager cleanup:', error);
    }
  }
}

module.exports = SyncManager;