/**
 * Data Processor for Limitless Plugin
 *
 * Handles processing, normalizing, and storing lifelog data.
 * Manages database operations and data validation.
 *
 * @module DataProcessor
 */

/**
 * Data Processor class for handling lifelog data operations
 *
 * @class DataProcessor
 */
class DataProcessor {
  /**
   * Creates a new DataProcessor instance
   *
   * @param {Object} api - The plugin API instance
   * @param {Object} logger - The logger instance
   */
  constructor(api, logger) {
    this.api = api;
    this.logger = logger;
    this.tableName = 'limitless_lifelogs';
  }

  /**
   * Ensures the limitless_lifelogs table exists in the database
   *
   * @returns {Promise<void>}
   */
  async ensureTableExists() {
    try {
      // Check if table exists using a simple query
      const tableExists = await this.api.database.raw(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = ?
        )`,
        [this.tableName]
      );

      if (!tableExists.rows[0].exists) {
        await this.logger.warn('Limitless lifelogs table does not exist. Please run database migrations.');
        throw new Error('Database table limitless_lifelogs does not exist. Please run migrations.');
      }

      await this.logger.debug('Limitless lifelogs table exists');
    } catch (error) {
      await this.logger.error('Failed to verify table existence:', error);
      throw error;
    }
  }

  /**
   * Processes and stores lifelog data with UPSERT operations
   *
   * @param {Array} lifelogs - Array of lifelog objects from Limitless API
   * @returns {Promise<Object>} Processing result
   */
  async processLifelogs(lifelogs) {
    try {
      await this.logger.debug('Processing lifelogs', { count: lifelogs.length });

      // Ensure table exists
      await this.ensureTableExists();

      // Get current user ID
      const userId = this.api.user?.id;
      if (!userId) {
        throw new Error('User not authenticated');
      }

      if (!lifelogs || lifelogs.length === 0) {
        return {
          success: true,
          processed: 0,
          inserted: 0,
          updated: 0
        };
      }

      let inserted = 0;
      let updated = 0;
      const trx = await this.api.database.transaction();

      try {
        for (const lifelog of lifelogs) {
          // Validate lifelog data
          if (!this.validateLifelogData(lifelog)) {
            await this.logger.warn('Invalid lifelog data, skipping', { 
              lifelogId: lifelog.id 
            });
            continue;
          }

          // Map lifelog data to database schema
          const mappedData = this.mapLifelogToSchema(lifelog, userId);

          // Perform UPSERT operation
          const result = await trx.raw(`
            INSERT INTO ${this.tableName} (
              user_id, lifelog_id, title, markdown, contents, 
              start_time, end_time, created_at, updated_at, metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT (user_id, lifelog_id) 
            DO UPDATE SET
              title = EXCLUDED.title,
              markdown = EXCLUDED.markdown,
              contents = EXCLUDED.contents,
              start_time = EXCLUDED.start_time,
              end_time = EXCLUDED.end_time,
              updated_at = EXCLUDED.updated_at,
              metadata = EXCLUDED.metadata
            RETURNING (xmax = 0) AS inserted
          `, [
            mappedData.user_id,
            mappedData.lifelog_id,
            mappedData.title,
            mappedData.markdown,
            mappedData.contents,
            mappedData.start_time,
            mappedData.end_time,
            mappedData.created_at,
            mappedData.updated_at,
            mappedData.metadata
          ]);

          // Track if this was an insert or update
          if (result.rows[0].inserted) {
            inserted++;
          } else {
            updated++;
          }
        }

        await trx.commit();

        await this.logger.info('Lifelogs processed successfully', {
          processed: lifelogs.length,
          inserted,
          updated
        });

        return {
          success: true,
          processed: lifelogs.length,
          inserted,
          updated
        };

      } catch (error) {
        await trx.rollback();
        throw error;
      }

    } catch (error) {
      await this.logger.error('Failed to process lifelogs:', error);
      throw error;
    }
  }

  /**
   * Validates lifelog data structure
   *
   * @private
   * @param {Object} lifelog - Lifelog object to validate
   * @returns {boolean} Whether the lifelog data is valid
   */
  validateLifelogData(lifelog) {
    if (!lifelog || typeof lifelog !== 'object') {
      return false;
    }

    // Check required fields
    if (!lifelog.id || typeof lifelog.id !== 'string') {
      return false;
    }

    // Check that we have either a title or some content
    if (!lifelog.title && !lifelog.markdown && !lifelog.contents) {
      return false;
    }

    return true;
  }

  /**
   * Maps lifelog data from Limitless API format to database schema
   *
   * @private
   * @param {Object} lifelog - Lifelog object from API
   * @param {string} userId - User ID
   * @returns {Object} Mapped data for database insertion
   */
  mapLifelogToSchema(lifelog, userId) {
    const now = new Date().toISOString();
    
    return {
      user_id: userId,
      lifelog_id: lifelog.id,
      title: lifelog.title || null,
      markdown: lifelog.markdown || null,
      contents: lifelog.contents ? JSON.stringify(lifelog.contents) : null,
      start_time: lifelog.startTime ? new Date(lifelog.startTime).toISOString() : null,
      end_time: lifelog.endTime ? new Date(lifelog.endTime).toISOString() : null,
      created_at: now,
      updated_at: now,
      metadata: JSON.stringify({
        originalId: lifelog.id,
        processedAt: now,
        sourceAPI: 'limitless',
        ...(lifelog.metadata || {})
      })
    };
  }

  /**
   * Gets the last sync timestamp from plugin settings
   *
   * @returns {Promise<string|null>} Last sync timestamp or null
   */
  async getLastSyncTimestamp() {
    try {
      const settings = await this.api.settings.get();
      return settings?.lastSyncTime || null;
    } catch (error) {
      await this.logger.error('Failed to get last sync timestamp:', error);
      return null;
    }
  }

  /**
   * Updates the last sync timestamp in plugin settings
   *
   * @param {string} timestamp - ISO timestamp
   * @returns {Promise<void>}
   */
  async updateLastSyncTimestamp(timestamp) {
    try {
      const settings = await this.api.settings.get() || {};
      settings.lastSyncTime = timestamp;
      await this.api.settings.set(settings);
      
      await this.logger.debug('Updated last sync timestamp', { timestamp });
    } catch (error) {
      await this.logger.error('Failed to update last sync timestamp:', error);
    }
  }

  /**
   * Retrieves stored lifelog data with filtering options
   *
   * @param {Object} [options] - Query options
   * @param {number} [options.limit=100] - Maximum number of records to return
   * @param {number} [options.offset=0] - Number of records to skip
   * @param {string} [options.startDate] - Start date filter (ISO string)
   * @param {string} [options.endDate] - End date filter (ISO string)
   * @param {string} [options.searchTerm] - Search term for title/markdown
   * @returns {Promise<Array>} Array of stored lifelog records
   */
  async getStoredLifelogs(options = {}) {
    try {
      const {
        limit = 100,
        offset = 0,
        startDate,
        endDate,
        searchTerm
      } = options;

      const userId = this.api.user?.id;
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Build query
      let query = this.api.database(this.tableName)
        .where('user_id', userId)
        .orderBy('start_time', 'desc')
        .limit(limit)
        .offset(offset);

      // Apply date filters
      if (startDate) {
        query = query.where('start_time', '>=', startDate);
      }
      if (endDate) {
        query = query.where('start_time', '<=', endDate);
      }

      // Apply search filter
      if (searchTerm) {
        query = query.where(builder => {
          builder
            .where('title', 'ilike', `%${searchTerm}%`)
            .orWhere('markdown', 'ilike', `%${searchTerm}%`);
        });
      }

      const results = await query;

      // Transform results
      return results.map(record => ({
        ...record,
        contents: record.contents ? JSON.parse(record.contents) : null,
        metadata: record.metadata ? JSON.parse(record.metadata) : {}
      }));

    } catch (error) {
      await this.logger.error('Failed to retrieve stored lifelogs:', error);
      throw error;
    }
  }

  /**
   * Gets statistics about stored lifelog data
   *
   * @returns {Promise<Object>} Statistics object
   */
  async getStorageStats() {
    try {
      const userId = this.api.user?.id;
      if (!userId) {
        throw new Error('User not authenticated');
      }

      const stats = await this.api.database(this.tableName)
        .where('user_id', userId)
        .select([
          this.api.database.raw('COUNT(*) as total_records'),
          this.api.database.raw('MIN(start_time) as earliest_lifelog'),
          this.api.database.raw('MAX(start_time) as latest_lifelog'),
          this.api.database.raw('MAX(updated_at) as last_updated')
        ])
        .first();

      return {
        totalRecords: parseInt(stats.total_records) || 0,
        earliestLifelog: stats.earliest_lifelog,
        latestLifelog: stats.latest_lifelog,
        lastUpdated: stats.last_updated
      };

    } catch (error) {
      await this.logger.error('Failed to get storage stats:', error);
      throw error;
    }
  }

  /**
   * Clears all lifelog data for the current user
   *
   * @returns {Promise<number>} Number of records deleted
   */
  async clearLifelogData() {
    try {
      const userId = this.api.user?.id;
      if (!userId) {
        throw new Error('User not authenticated');
      }

      const count = await this.api.database(this.tableName)
        .where('user_id', userId)
        .del();

      await this.logger.info(`Cleared ${count} lifelog records`);
      return count;

    } catch (error) {
      await this.logger.error('Failed to clear lifelog data:', error);
      throw error;
    }
  }
}

module.exports = DataProcessor;