{"author": "Lifeboard Team", "description": "Import and process lifelogs from Limitless AI pendant", "homepage": "https://lifeboard.app/plugins/limitless", "id": "limitless", "main": "main.js", "minAppVersion": "0.1.0", "name": "Limitless AI Integration", "permissions": ["network", "filesystem", "workspace"], "settings": {"apiKey": {"description": "Limitless AI API Key", "sensitive": true, "type": "string"}, "autoSync": {"default": true, "description": "Enable automatic data synchronization", "type": "boolean"}, "maxRecords": {"default": 1000, "description": "Maximum number of lifelogs to store locally", "type": "number"}, "syncInterval": {"default": 21600, "description": "Sync interval in seconds (default: 6 hours)", "type": "number"}, "timezone": {"default": "UTC", "description": "Timezone for date operations", "type": "string"}}, "version": "1.0.0"}