/**
 * Settings UI JavaScript
 *
 * Handles all user interactions in the Limitless AI settings interface,
 * including API key validation, form management, and real-time status updates.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

console.log('🚨🚨🚨 CRITICAL: settings-ui.js file loaded at', new Date().toISOString());

// ROBUST DEBUG LOGGING - Multiple methods to ensure visibility
const fs = require('fs');
const path = require('path');

// Create debug log function that writes to multiple places
function debugLog(message, data = null) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  const fullMessage = data ? `${logMessage} ${JSON.stringify(data)}` : logMessage;

  // 1. Console log (should appear in main terminal)
  console.log('🚨 DEBUG:', fullMessage);

  // 2. Write to debug file
  try {
    const debugFile = path.join(__dirname, '../../../../debug_limitless.log');
    fs.appendFileSync(debugFile, fullMessage + '\n');
  } catch (e) {
    console.error('Failed to write debug log:', e);
  }

  // 3. Alert for critical messages (will be visible immediately)
  if (message.includes('CRITICAL') || message.includes('saveSettings')) {
    setTimeout(() => alert(`DEBUG: ${message}`), 100);
  }
}

debugLog('🚨🚨🚨 CRITICAL: settings-ui.js file loaded with robust debugging');

const Logger = require('./logger');

class LimitlessSettingsUI {
  /**
   * Creates a new LimitlessSettingsUI instance
   */
  constructor() {
    this.pluginAPI = null;
    this.limitlessAPI = null;
    this.currentSettings = {};
    this.validationInProgress = false;
    this.logger = null;

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initialize());
    } else {
      this.initialize();
    }
  }

  /**
   * Initializes the settings UI
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    debugLog('🚨 CRITICAL: LimitlessSettingsUI.initialize() called');

    // Add visible indicator that JavaScript is loaded
    try {
      const body = document.body;
      if (body) {
        const indicator = document.createElement('div');
        indicator.id = 'js-loaded-indicator';
        indicator.style.cssText = 'position: fixed; top: 10px; right: 10px; background: green; color: white; padding: 5px; z-index: 9999; font-size: 12px;';
        indicator.textContent = '✅ JS LOADED';
        body.appendChild(indicator);
        debugLog('🚨 CRITICAL: Added visible JS loaded indicator to page');
      }
    } catch (e) {
      debugLog('🚨 ERROR: Failed to add JS indicator', e);
    }

    try {
      // Get plugin API reference from parent window
      this.pluginAPI = window.parent?.pluginAPI || window.pluginAPI;

      if (!this.pluginAPI) {
        console.error('Plugin API not available');
        this.showError('Plugin API not available. Please reload the plugin.');
        return;
      }

      // Plugin API is available - we'll use lifeboard global for validation

      // Prefer file logger if possible
      try {
        this.logger = new Logger(this.pluginAPI);
        // Test file logger immediately
        await this.logger.info('Logger test: File logger initialized and should write to /logs');
        console.log('LimitlessSettingsUI: File logger initialized. Logs should appear in /logs.');
      } catch (e) {
        console.error('LimitlessSettingsUI: Failed to initialize file logger:', e);
        // Fallback to pluginAPI.getLogger or console
        this.logger = this.pluginAPI.getLogger ? this.pluginAPI.getLogger('limitless-settings-ui') : null;
        if (this.logger && this.logger.info) {
          this.logger.info('Logger fallback: Using pluginAPI.getLogger or console', { error: e.message });
        } else {
          console.warn('Logger fallback: Using console only. File logging unavailable.');
        }
      }

      if (this.logger && this.logger.info) {
        this.logger.info('Limitless Settings UI initializing', {
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        });
      } else if (this.logger && this.logger.INFO) {
        this.logger.INFO('Limitless Settings UI initializing', {
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        });
      } else {
        console.log('Limitless Settings UI initializing', {
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        });
      }

      // Load current settings
      await this.loadSettings();

      // Set up event listeners
      this.setupEventListeners();

      // Update UI with current settings
      this.updateUI();

      if (this.logger) {
        this.logger.INFO('Limitless Settings UI initialized successfully', {
          settingsCount: Object.keys(this.currentSettings).length,
          hasApiKey: !!this.currentSettings.apiKey
        });
      } else {
        console.log('Limitless Settings UI initialized successfully');
      }

    } catch (error) {
      if (this.logger) {
        this.logger.ERROR('Failed to initialize settings UI', {
          error: error.message,
          stack: error.stack
        });
      } else {
        console.error('Failed to initialize settings UI:', error);
      }
      this.showError('Failed to initialize settings interface.');
    }
  }

  /**
   * Sets up all event listeners for UI elements
   */
  setupEventListeners() {
    // API Key input - enable/disable save button based on content
    const apiKeyInput = document.getElementById('apiKey');
    if (apiKeyInput) {
      // Set up input event listener for real-time validation and save button control
      apiKeyInput.addEventListener('input', () => this.handleAPIKeyInput());

      // Set up paste event listener
      apiKeyInput.addEventListener('paste', () => {
        // Use setTimeout to allow paste to complete before checking
        setTimeout(() => {
          this.handleAPIKeyInput();
          this.clearValidationStatus();
        }, 10);
      });

      // Initial check on page load
      this.handleAPIKeyInput();
    }

    // API Key validation
    const validateBtn = document.getElementById('validateBtn');
    if (validateBtn) {
      validateBtn.addEventListener('click', () => this.validateAPIKey());
    }

    // Show/Hide API key
    const showKeyBtn = document.getElementById('showKeyBtn');
    if (showKeyBtn) {
      showKeyBtn.addEventListener('click', () => this.toggleAPIKeyVisibility());
    }

    // Auto-sync checkbox
    const autoSyncCheckbox = document.getElementById('autoSync');
    if (autoSyncCheckbox) {
      autoSyncCheckbox.addEventListener('change', () => this.handleAutoSyncChange());
    }

    // Sync interval range
    const syncIntervalRange = document.getElementById('syncInterval');
    if (syncIntervalRange) {
      syncIntervalRange.addEventListener('input', () => this.handleSyncIntervalChange());
    }

    // Action buttons
    const syncNowBtn = document.getElementById('syncNowBtn');
    if (syncNowBtn) {
      syncNowBtn.addEventListener('click', () => this.performManualSync());
    }

    const testConnectionBtn = document.getElementById('testConnectionBtn');
    if (testConnectionBtn) {
      testConnectionBtn.addEventListener('click', () => this.testConnection());
    }

    const saveBtn = document.getElementById('saveBtn');
    if (saveBtn) {
      debugLog('🚨 CRITICAL: Save button found, adding event listener');
      saveBtn.addEventListener('click', () => {
        debugLog('🚨 CRITICAL: Save button clicked - calling saveSettings');
        this.saveSettings();
      });
    } else {
      debugLog('🚨 CRITICAL ERROR: Save button not found in DOM');
    }

    const resetBtn = document.getElementById('resetBtn');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => this.resetSettings());
    }


  }

  /**
   * Loads current settings from plugin storage
   *
   * @returns {Promise<void>}
   */
  async loadSettings() {
    try {
      this.currentSettings = this.pluginAPI.storage.loadData() || {};

      // Set defaults for missing settings
      this.currentSettings = {
        apiKey: '',
        syncInterval: 6,
        autoSync: true,
        maxRecords: 1000,
        timezone: 'UTC',
        enableDebugLogging: false,
        aiEnhancement: true,
        ...this.currentSettings
      };

      if (this.logger) {
        this.logger.INFO('Settings loaded successfully', {
          settingsCount: Object.keys(this.currentSettings).length,
          hasApiKey: !!this.currentSettings.apiKey,
          syncInterval: this.currentSettings.syncInterval,
          autoSync: this.currentSettings.autoSync
        });
      }

    } catch (error) {
      if (this.logger) {
        this.logger.ERROR('Failed to load settings', {
          error: error.message,
          stack: error.stack
        });
      } else {
        console.error('Failed to load settings:', error);
      }
      this.showError('Failed to load current settings.');
    }
  }

  /**
   * Updates the UI with current settings values
   */
  updateUI() {
    try {
      // API Key (show masked)
      const apiKeyInput = document.getElementById('apiKey');
      if (apiKeyInput && this.currentSettings.apiKey) {
        apiKeyInput.value = this.currentSettings.apiKey;
      }

      // Update save button state based on API key presence
      this.handleAPIKeyInput();

      // Sync interval
      const syncIntervalInput = document.getElementById('syncInterval');
      const syncIntervalValue = document.getElementById('syncIntervalValue');
      if (syncIntervalInput) {
        syncIntervalInput.value = this.currentSettings.syncInterval;
        if (syncIntervalValue) {
          const hours = this.currentSettings.syncInterval;
          syncIntervalValue.textContent = `${hours} hour${hours !== 1 ? 's' : ''}`;
        }
      }

      // Auto sync checkbox
      const autoSyncCheckbox = document.getElementById('autoSync');
      if (autoSyncCheckbox) {
        autoSyncCheckbox.checked = this.currentSettings.autoSync;
      }

      // Max records
      const maxRecordsInput = document.getElementById('maxRecords');
      if (maxRecordsInput) {
        maxRecordsInput.value = this.currentSettings.maxRecords;
      }

      // Timezone
      const timezoneSelect = document.getElementById('timezone');
      if (timezoneSelect) {
        timezoneSelect.value = this.currentSettings.timezone;
      }

      // Debug logging
      const debugLoggingCheckbox = document.getElementById('enableDebugLogging');
      if (debugLoggingCheckbox) {
        debugLoggingCheckbox.checked = this.currentSettings.enableDebugLogging;
      }

      // AI enhancement
      const aiEnhancementCheckbox = document.getElementById('aiEnhancement');
      if (aiEnhancementCheckbox) {
        aiEnhancementCheckbox.checked = this.currentSettings.aiEnhancement;
      }

      // Update status indicators
      this.updateStatusIndicators();

    } catch (error) {
      console.error('Failed to update UI:', error);
    }
  }

  /**
   * Updates status indicators in the UI
   */
  updateStatusIndicators() {
    // API Status
    const apiStatus = document.getElementById('apiStatus');
    if (apiStatus) {
      const hasApiKey = !!this.currentSettings.apiKey;
      const statusDot = apiStatus.querySelector('.status-dot');
      const statusText = apiStatus.querySelector('.status-text');

      if (hasApiKey) {
        statusDot.className = 'status-dot connected';
        statusText.textContent = 'Connected';
      } else {
        statusDot.className = 'status-dot unknown';
        statusText.textContent = 'Not configured';
      }
    }

    // Auto Sync Status
    const autoSyncStatus = document.getElementById('autoSyncStatus');
    if (autoSyncStatus) {
      const statusDot = autoSyncStatus.querySelector('.status-dot');
      const statusText = autoSyncStatus.querySelector('.status-text');

      if (this.currentSettings.autoSync) {
        statusDot.className = 'status-dot enabled';
        statusText.textContent = 'Enabled';
      } else {
        statusDot.className = 'status-dot disabled';
        statusText.textContent = 'Disabled';
      }
    }
  }

  /**
   * Handles API key input changes
   */
  handleAPIKeyInput() {
    const apiKeyInput = document.getElementById('apiKey');
    if (apiKeyInput) {
      const apiKey = apiKeyInput.value.trim();

      // Clear previous validation status
      this.clearValidationStatus();

      // Enable/disable validate button
      const validateBtn = document.getElementById('validateBtn');
      if (validateBtn) {
        validateBtn.disabled = !apiKey || this.validationInProgress;
      }
    }
  }

  /**
   * Validates the API key with Limitless AI (for manual "Test Connection")
   *
   * @returns {Promise<void>}
   */
  async validateAPIKey() {
    const apiKeyInput = document.getElementById('apiKey');
    const validateBtn = document.getElementById('validateBtn');
    const validationStatus = document.getElementById('validationStatus');

    if (!apiKeyInput || this.validationInProgress) {
      return;
    }

    const apiKey = apiKeyInput.value.trim();
    if (!apiKey) {
      this.showValidationError('Please enter an API key');
      return;
    }

    try {
      this.validationInProgress = true;

      // Update UI to show validation in progress
      if (validateBtn) {
        validateBtn.disabled = true;
        validateBtn.textContent = 'Validating...';
      }

      if (validationStatus) {
        validationStatus.innerHTML = '<span class="validating">🔄 Validating API key...</span>';
      }

      // Use IPC to validate API key in main process where LimitlessAPI is available
      const lifeboardAPI = typeof lifeboard !== 'undefined' ? lifeboard : window.parent?.lifeboard;
      if (!lifeboardAPI) {
        throw new Error('lifeboard API not available');
      }

      const result = await lifeboardAPI.settings.save('limitless', { apiKey }, null, { validateOnly: true });

      if (result.success) {
        this.showValidationSuccess('API key is valid and ready to use!');

        // Update settings with validated key
        this.currentSettings.apiKey = apiKey;
        this.updateStatusIndicators();

      } else {
        const errorMessage = result.error || 'API key validation failed';
        this.showValidationError(errorMessage);
      }

    } catch (error) {
      console.error('API key validation error:', error);
      this.showValidationError('Failed to validate API key. Please check your connection.');

    } finally {
      this.validationInProgress = false;

      // Reset validate button
      if (validateBtn) {
        validateBtn.disabled = false;
        validateBtn.textContent = 'Validate';
      }
    }
  }

  /**
   * Trigger background validation after Phase 1 storage
   *
   * @param {string} correlationId - Correlation ID for tracking
   * @returns {Promise<void>}
   */
  async triggerBackgroundValidation(correlationId) {
    try {
      console.log('🔍 DEBUG: Triggering background validation', { correlationId });

      // Set up event listener for validation completion
      this.setupValidationEventListener();

      // Use IPC to trigger background validation
      const lifeboardAPI = typeof lifeboard !== 'undefined' ? lifeboard : window.parent?.lifeboard;
      if (!lifeboardAPI) {
        throw new Error('lifeboard API not available for background validation');
      }

      // Trigger background validation via IPC
      await lifeboardAPI.settings.validateBackground('limitless', correlationId);

      console.log('🔍 DEBUG: Background validation triggered successfully');

    } catch (error) {
      console.error('Failed to trigger background validation:', error);
      this.showValidationError('Failed to start validation. Please try again.');
    }
  }

  /**
   * Set up event listener for validation completion
   *
   * @private
   */
  setupValidationEventListener() {
    // Remove any existing listener to prevent duplicates
    if (this.validationEventListener) {
      window.removeEventListener('limitless:validation:complete', this.validationEventListener);
    }

    // Create new event listener
    this.validationEventListener = (event) => {
      this.handleValidationComplete(event.detail);
    };

    // Add event listener
    window.addEventListener('limitless:validation:complete', this.validationEventListener);
  }

  /**
   * Handle validation completion event
   *
   * @param {Object} eventData - Validation completion data
   * @private
   */
  handleValidationComplete(eventData) {
    const { pluginId, isValid, correlationId, error } = eventData;

    console.log('🔍 DEBUG: Validation completion event received', {
      pluginId,
      isValid,
      correlationId,
      hasError: !!error
    });

    if (pluginId !== 'limitless') {
      return; // Not for this plugin
    }

    const validationStatus = document.getElementById('validationStatus');

    if (isValid) {
      // Validation successful
      if (validationStatus) {
        validationStatus.innerHTML = '<span class="success">✅ API key validated successfully!</span>';
      }

      // Enable plugin functionality
      if (window.parent && window.parent.updatePluginEnablementStatus) {
        console.log('🔍 DEBUG: Updating plugin enablement status to true');
        window.parent.updatePluginEnablementStatus('limitless', true, true);
      }

      // Clear validation status after delay
      setTimeout(() => {
        this.clearValidationStatus();
      }, 5000);

    } else {
      // Validation failed
      const errorMessage = error?.message || 'API key validation failed';
      if (validationStatus) {
        validationStatus.innerHTML = `<span class="error">❌ Validation failed: ${errorMessage}</span>`;
      }

      // Plugin remains disabled
      if (window.parent && window.parent.updatePluginEnablementStatus) {
        console.log('🔍 DEBUG: Keeping plugin enablement status false');
        window.parent.updatePluginEnablementStatus('limitless', false, false);
      }
    }

    // Remove event listener after handling
    if (this.validationEventListener) {
      window.removeEventListener('limitless:validation:complete', this.validationEventListener);
      this.validationEventListener = null;
    }
  }

  /**
   * Shows validation success message
   *
   * @param {string} message - Success message to display
   */
  showValidationSuccess(message) {
    const validationStatus = document.getElementById('validationStatus');
    if (validationStatus) {
      validationStatus.innerHTML = `<span class="validation-success">✅ ${message}</span>`;
    }
  }

  /**
   * Shows validation error message
   *
   * @param {string} message - Error message to display
   */
  showValidationError(message) {
    console.log('Limitless Settings: Showing validation error:', message);

    const validationStatus = document.getElementById('validationStatus');
    if (validationStatus) {
      validationStatus.innerHTML = `<span class="validation-error">❌ ${message}</span>`;
      validationStatus.style.display = 'block';
      validationStatus.style.backgroundColor = '#fee';
      validationStatus.style.border = '1px solid #f00';
      validationStatus.style.padding = '10px';
      validationStatus.style.borderRadius = '4px';
      validationStatus.style.marginTop = '10px';
      console.log('Limitless Settings: Updated inline validation status');
    }

    // Enhanced error messaging for invalid API key workflow
    console.log('Limitless Settings: Displaying error modal for validation failure');
    this.showErrorModal('API Key Validation Failed',
      `${message}\n\nThe API key was NOT saved. Please provide a valid API key and try again.\n\nThe plugin cannot be enabled until a valid API key is provided.`);

    // Ensure plugin cannot be enabled with invalid API key
    if (window.parent && window.parent.updatePluginEnablementStatus) {
      window.parent.updatePluginEnablementStatus('limitless', false, false);
    }
  }

  /**
   * Clears validation status display
   */
  clearValidationStatus() {
    const validationStatus = document.getElementById('validationStatus');
    if (validationStatus) {
      validationStatus.innerHTML = '';
    }
  }

  /**
   * Toggles API key visibility
   */
  toggleAPIKeyVisibility() {
    const apiKeyInput = document.getElementById('apiKey');
    const showKeyBtn = document.getElementById('showKeyBtn');

    if (apiKeyInput && showKeyBtn) {
      const isPassword = apiKeyInput.type === 'password';
      apiKeyInput.type = isPassword ? 'text' : 'password';
      showKeyBtn.textContent = isPassword ? '🙈' : '👁️';
      showKeyBtn.title = isPassword ? 'Hide API Key' : 'Show API Key';
    }
  }

  /**
   * Handles auto-sync checkbox change
   */
  handleAutoSyncChange() {
    const autoSyncCheckbox = document.getElementById('autoSync');
    if (autoSyncCheckbox) {
      this.currentSettings.autoSync = autoSyncCheckbox.checked;
      this.updateStatusIndicators();
    }
  }

  /**
   * Handles sync interval range change
   */
  handleSyncIntervalChange() {
    const syncIntervalInput = document.getElementById('syncInterval');
    const syncIntervalValue = document.getElementById('syncIntervalValue');

    if (syncIntervalInput) {
      const hours = parseInt(syncIntervalInput.value);
      this.currentSettings.syncInterval = hours;

      if (syncIntervalValue) {
        syncIntervalValue.textContent = `${hours} hour${hours !== 1 ? 's' : ''}`;
      }
    }
  }

  /**
   * Handles API key input changes - enables/disables save button
   */
  handleAPIKeyInput() {
    const apiKeyInput = document.getElementById('apiKey');
    const saveBtn = document.getElementById('saveBtn');

    if (!apiKeyInput || !saveBtn) {
      return;
    }

    const apiKeyValue = apiKeyInput.value.trim();
    const hasApiKey = apiKeyValue.length > 0;

    // Enable/disable save button based on API key presence
    saveBtn.disabled = !hasApiKey;

    // Update button appearance and text
    if (hasApiKey) {
      saveBtn.textContent = 'Save Settings';
      saveBtn.classList.remove('disabled');
      saveBtn.title = 'Save your Limitless AI settings';
    } else {
      saveBtn.textContent = 'Save Settings';
      saveBtn.classList.add('disabled');
      saveBtn.title = 'Enter an API key to enable saving';
    }

    // Clear any existing validation status when user types
    this.clearValidationStatus();

    console.log('🔍 DEBUG: API key input changed', {
      hasApiKey,
      keyLength: apiKeyValue.length,
      saveButtonDisabled: saveBtn.disabled
    });
  }

  /**
   * Performs manual sync
   *
   * @returns {Promise<void>}
   */
  async performManualSync() {
    const syncNowBtn = document.getElementById('syncNowBtn');
    const syncProgress = document.getElementById('syncProgress');
    const progressText = document.getElementById('progressText');

    try {
      // Validate API key exists
      if (!this.currentSettings.apiKey) {
        this.showError('Please configure and validate your API key first.');
        return;
      }

      // Update UI for sync in progress
      if (syncNowBtn) {
        syncNowBtn.disabled = true;
        syncNowBtn.innerHTML = '<span class="btn-icon">⏳</span> Syncing...';
      }

      if (syncProgress) {
        syncProgress.style.display = 'block';
      }

      if (progressText) {
        progressText.textContent = 'Starting synchronization...';
      }

      // Call plugin's sync functionality
      if (this.pluginAPI.commands) {
        await this.pluginAPI.commands.execute('limitless-sync-now');

        if (progressText) {
          progressText.textContent = 'Synchronization completed successfully!';
        }

        // Update last sync time
        this.updateLastSyncTime();

      } else {
        throw new Error('Plugin commands not available');
      }

    } catch (error) {
      console.error('Manual sync failed:', error);
      this.showError('Synchronization failed. Please check your API key and connection.');

      if (progressText) {
        progressText.textContent = 'Synchronization failed.';
      }

    } finally {
      // Reset UI
      if (syncNowBtn) {
        syncNowBtn.disabled = false;
        syncNowBtn.innerHTML = '<span class="btn-icon">🔄</span> Sync Now';
      }

      // Hide progress after delay
      setTimeout(() => {
        if (syncProgress) {
          syncProgress.style.display = 'none';
        }
      }, 3000);
    }
  }

  /**
   * Tests connection to Limitless API
   *
   * @returns {Promise<void>}
   */
  async testConnection() {
    const testConnectionBtn = document.getElementById('testConnectionBtn');

    try {
      if (!this.currentSettings.apiKey) {
        this.showError('Please configure your API key first.');
        return;
      }

      if (testConnectionBtn) {
        testConnectionBtn.disabled = true;
        testConnectionBtn.innerHTML = '<span class="btn-icon">⏳</span> Testing...';
      }

      // Perform API key validation as connection test
      await this.validateAPIKey();

    } catch (error) {
      console.error('Connection test failed:', error);
      this.showError('Connection test failed.');

    } finally {
      if (testConnectionBtn) {
        testConnectionBtn.disabled = false;
        testConnectionBtn.innerHTML = '<span class="btn-icon">🔍</span> Test Connection';
      }
    }
  }

  /**
   * Saves current settings using two-phase validation approach
   *
   * @returns {Promise<void>}
   */
  async saveSettings() {
    debugLog('🚨🚨🚨 CRITICAL: saveSettings() method called - START OF EXECUTION');

    // Prevent multiple simultaneous saves
    if (this.saveInProgress) {
      debugLog('🚨 CRITICAL: Save already in progress, ignoring duplicate call');
      return;
    }
    this.saveInProgress = true;

    const saveBtn = document.getElementById('saveBtn');
    const sessionId = `save_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // CRITICAL DEBUG: Log everything about the current state
    console.log('🚨 CRITICAL DEBUG: saveSettings called', {
      sessionId,
      hasLifeboard: typeof lifeboard !== 'undefined',
      lifeboardType: typeof lifeboard,
      lifeboardKeys: typeof lifeboard === 'object' ? Object.keys(lifeboard) : 'not object',
      hasSettingsAPI: typeof lifeboard?.settings !== 'undefined',
      hasSaveMethod: typeof lifeboard?.settings?.save !== 'undefined',
      currentURL: window.location.href,
      isInModal: window !== window.parent,
      parentHasLifeboard: typeof window.parent?.lifeboard !== 'undefined'
    });

    try {
      console.log('🔍 DEBUG: Starting two-phase save process', { sessionId });
      if (this.logger) {
        this.logger.INFO('Starting two-phase save process', { sessionId });
      } else {
        console.log('Limitless Settings: Starting two-phase save process');
      }

      // Collect all form values
      const formData = this.collectFormData();

      // Safety check for form data collection
      if (!formData) {
        console.log('🚨 CRITICAL: Form data collection failed, showing error and returning early');
        this.showError('Failed to collect form data. Please try again.');

        if (saveBtn) {
          saveBtn.disabled = false;
          saveBtn.textContent = 'Save Settings';
        }
        return;
      }

      console.log('🔍 DEBUG: Form data collected', {
        sessionId,
        hasApiKey: !!formData.apiKey,
        apiKeyLength: formData.apiKey?.length || 0,
        apiKeyFirst4: formData.apiKey?.substring(0, 4) || 'none',
        syncInterval: formData.syncInterval,
        autoSync: formData.autoSync
      });

      if (this.logger) {
        this.logger.INFO('Form data collected', {
          sessionId,
          hasApiKey: !!formData.apiKey,
          apiKeyLength: formData.apiKey?.length || 0,
          syncInterval: formData.syncInterval,
          autoSync: formData.autoSync
        });
      } else {
        console.log('Limitless Settings: Form data collected', {
          hasApiKey: !!formData.apiKey,
          apiKeyLength: formData.apiKey?.length || 0,
          syncInterval: formData.syncInterval,
          autoSync: formData.autoSync
        });
      }

      // Phase 1: Format validation and immediate storage
      console.log('🔍 DEBUG: Phase 1 - Format validation', {
        sessionId,
        apiKey: formData.apiKey,
        apiKeyType: typeof formData.apiKey,
        apiKeyLength: formData.apiKey?.length || 0,
        apiKeyTruthy: !!formData.apiKey,
        apiKeyFalsy: !formData.apiKey,
        apiKeyEmptyString: formData.apiKey === '',
        apiKeyStrictCheck: formData.apiKey?.length === 0
      });

      // Robust API key validation
      if (!formData.apiKey ||
          typeof formData.apiKey !== 'string' ||
          formData.apiKey.trim().length === 0) {

        console.log('🚨 CRITICAL: Invalid or missing API key, showing error and returning early', {
          sessionId,
          apiKey: formData.apiKey,
          apiKeyType: typeof formData.apiKey,
          apiKeyLength: formData.apiKey?.length || 0,
          apiKeyTrimmed: formData.apiKey?.trim?.(),
          apiKeyTrimmedLength: formData.apiKey?.trim?.()?.length || 0,
          validationPath: 'early-return-invalid-api-key',
          shouldReturn: true
        });

        this.showError('API key is required.');

        // Ensure button is re-enabled
        if (saveBtn) {
          saveBtn.disabled = false;
          saveBtn.textContent = 'Save Settings';
        }

        console.log('🚨 CRITICAL: Returning early due to invalid API key - EXECUTION SHOULD STOP HERE');
        console.log('🚨 CRITICAL: If you see any more logs after this, there is a bug in the flow');
        return;
      }

      console.log('🔍 DEBUG: Phase 1 - Format validation passed, proceeding with immediate storage');

      if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.textContent = 'Saving...';
      }

      // Clear any previous validation status
      this.clearValidationStatus();

      // Phase 1: Immediate storage without network validation
      const validationStatus = document.getElementById('validationStatus');
      if (validationStatus) {
        validationStatus.innerHTML = '<span class="validating">💾 Saving API key...</span>';
      }

      // Phase 1: Immediate storage via IPC
      console.log('🔍 DEBUG: Phase 1 - Immediate storage via IPC', { sessionId });
      if (this.logger) {
        this.logger.INFO('Phase 1 - Immediate storage', { sessionId });
      } else {
        console.log('Limitless Settings: Phase 1 - Immediate storage');
      }

      // Check for lifeboard global in current window or parent window
      let lifeboardAPI = null;
      if (typeof lifeboard !== 'undefined') {
        console.log('🔍 DEBUG: Using lifeboard from current window');
        lifeboardAPI = lifeboard;
      } else if (typeof window.parent?.lifeboard !== 'undefined') {
        console.log('🔍 DEBUG: Using lifeboard from parent window');
        lifeboardAPI = window.parent.lifeboard;
      } else {
        console.log('🚨 CRITICAL: lifeboard global not available in current or parent window');
        this.showError('Cannot save settings: System error. Please reload the plugin.');
        return;
      }

      let saveResult;
      try {
        // Phase 1: Save immediately without network validation
        console.log('🔍 DEBUG: Calling lifeboardAPI.settings.save for immediate storage:', {
          pluginId: 'limitless',
          hasApiKey: !!formData.apiKey,
          apiKeyFirst4: formData.apiKey?.substring(0, 4),
          options: { immediateStorage: true }
        });

        saveResult = await lifeboardAPI.settings.save('limitless', formData, null, { immediateStorage: true });

        console.log('🔍 DEBUG: Immediate storage result received:', {
          result: saveResult,
          type: typeof saveResult,
          isObject: typeof saveResult === 'object',
          hasSuccess: saveResult?.hasOwnProperty('success'),
          successValue: saveResult?.success,
          errorValue: saveResult?.error
        });

      } catch (storageError) {
        const errorMsg = `Immediate storage failed: ${storageError.message}`;
        console.log('🔍 DEBUG: Storage error caught:', storageError);
        if (this.logger) {
          this.logger.ERROR(errorMsg, { sessionId, error: storageError });
        } else {
          console.error('Limitless Settings:', errorMsg, storageError);
        }
        this.showError('Failed to save settings. Please try again.');
        return;
      }

      // Check if immediate storage was successful
      const isStorageSuccessful = saveResult &&
                                 typeof saveResult === 'object' &&
                                 saveResult.success === true;

      console.log('🔍 DEBUG: Storage success check:', {
        sessionId,
        saveResult,
        saveResultType: typeof saveResult,
        isStorageSuccessful,
        hasSuccessProperty: saveResult?.hasOwnProperty('success'),
        successValue: saveResult?.success,
        errorValue: saveResult?.error
      });

      if (!isStorageSuccessful) {
        // Storage failed - show error and don't proceed
        const errorMessage = saveResult?.error || 'Failed to save settings';
        console.log('🔍 DEBUG: Storage failed, NOT proceeding to validation', {
          sessionId,
          error: errorMessage,
          saveResult,
          isStorageSuccessful
        });

        if (this.logger) {
          this.logger.ERROR('Settings storage failed', {
            sessionId,
            error: errorMessage
          });
        } else {
          console.error('Limitless Settings: Storage failed', {
            error: errorMessage
          });
        }

        this.showError(`Failed to save settings: ${errorMessage}`);
        return;
      }

      // Phase 1 successful - update UI and trigger Phase 2
      console.log('🔍 DEBUG: Phase 1 successful, proceeding to Phase 2');
      this.currentSettings = formData;

      // Update validation status to show Phase 2 starting
      const validationStatusEl = document.getElementById('validationStatus');
      if (validationStatusEl) {
        validationStatusEl.innerHTML = '<span class="validating">✅ API key saved. 🔄 Validating...</span>';
      }

      // Phase 2: Trigger background validation
      await this.triggerBackgroundValidation(sessionId);

      // Show immediate success message
      this.showSuccess('Settings saved successfully! Validation in progress...');
      this.updateStatusIndicators();

      if (this.logger) {
        this.logger.INFO('Phase 1 complete - settings saved, validation triggered', {
          sessionId,
          settingsCount: Object.keys(formData).length,
          hasApiKey: !!formData.apiKey,
          syncInterval: formData.syncInterval
        });
      }

    } catch (error) {
      console.log('🚨 CRITICAL: Error caught in saveSettings:', {
        error: error.message,
        stack: error.stack,
        sessionId
      });

      if (this.logger) {
        this.logger.ERROR('Failed to save settings', {
          sessionId,
          error: error.message,
          stack: error.stack
        });
      } else {
        console.error('Failed to save settings:', error);
      }

      // CRITICAL: Make sure we show an error, not a success message
      console.log('🚨 CRITICAL: Showing error message to user');
      this.showError('Failed to save settings. Please try again.');

      // Clear validation status on error
      this.clearValidationStatus();

    } finally {
      // Reset save in progress flag
      this.saveInProgress = false;

      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = 'Save Settings';
      }

      console.log('🚨 CRITICAL: saveSettings() method completed - END OF EXECUTION');
    }
  }

  /**
   * Collects all form data
   *
   * @returns {Object} Form data object
   collectFormData() {
     const apiKeyInput = document.getElementById('apiKey');
     const syncIntervalInput = document.getElementById('syncInterval');
     const autoSyncCheckbox = document.getElementById('autoSync');
     const maxRecordsInput = document.getElementById('maxRecords');
     const timezoneSelect = document.getElementById('timezone');
     const debugLoggingCheckbox = document.getElementById('enableDebugLogging');
     const aiEnhancementCheckbox = document.getElementById('aiEnhancement');

     const formData = {
       apiKey: apiKeyInput?.value.trim() || '',
       syncInterval: parseInt(syncIntervalInput?.value) || 6,
       autoSync: autoSyncCheckbox?.checked || false,
       maxRecords: parseInt(maxRecordsInput?.value) || 1000,
       timezone: timezoneSelect?.value || 'UTC',
       enableDebugLogging: debugLoggingCheckbox?.checked || false,
       aiEnhancement: aiEnhancementCheckbox?.checked || true
     };

     // DEBUG: Log form data collection details
     console.log('🔍 DEBUG: collectFormData() called', {
       apiKeyRaw: apiKeyInput?.value,
       apiKeyTrimmed: apiKeyInput?.value.trim(),
       apiKeyFinal: formData.apiKey,
       apiKeyLength: formData.apiKey.length,
       apiKeyTruthy: !!formData.apiKey,
       apiKeyFalsy: !formData.apiKey,
       inputExists: !!apiKeyInput,
       inputValue: apiKeyInput?.value,
       timestamp: new Date().toISOString()
     });

     return formData;
   }

  /**
   * Resets settings to defaults
   *
   * @returns {Promise<void>}
   */
  async resetSettings() {
    const resetBtn = document.getElementById('resetBtn');

    try {
      // Confirm reset action
      const confirmed = confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.');
      if (!confirmed) {
        return;
      }

      if (resetBtn) {
        resetBtn.disabled = true;
        resetBtn.textContent = 'Resetting...';
      }

      // Reset to default settings
      this.currentSettings = {
        apiKey: '',
        syncInterval: 6,
        autoSync: true,
        maxRecords: 1000,
        timezone: 'UTC',
        enableDebugLogging: false,
        aiEnhancement: true
      };

      // Save defaults and update UI
      const success = this.pluginAPI.storage.saveData(this.currentSettings);

      if (success) {
        this.updateUI();
        this.clearValidationStatus();
        this.showSuccess('Settings reset to defaults successfully!');
      } else {
        throw new Error('Failed to reset settings');
      }

    } catch (error) {
      console.error('Failed to reset settings:', error);
      this.showError('Failed to reset settings. Please try again.');

    } finally {
      if (resetBtn) {
        resetBtn.disabled = false;
        resetBtn.textContent = 'Reset to Defaults';
      }
    }
  }

  /**
   * Updates the last sync time display
   */
  updateLastSyncTime() {
    const lastSyncInfo = document.getElementById('lastSyncInfo');
    if (lastSyncInfo) {
      const now = new Date();
      const statusText = lastSyncInfo.querySelector('.status-text');
      if (statusText) {
        statusText.textContent = now.toLocaleString();
      }
    }
  }

  /**
   * Shows success message
   *
   * @param {string} message - Success message to display
   */
  showSuccess(message) {
    // You could implement a toast notification or status bar here
    console.log('Success:', message);
    alert(message); // Temporary implementation
  }

  /**
   * Creates a logger adapter that matches the LimitlessAPI expected interface
   *
   * @returns {Object} Logger adapter with async methods
   */
  createLoggerAdapter() {
    return {
      startTimer: (name) => ({
        stop: async () => {
          // Return a mock duration for timing
          return 100;
        }
      }),
      info: async (message, data) => {
        if (this.logger) {
          this.logger.INFO(message, data);
        } else {
          console.log(`[INFO] ${message}`, data || '');
        }
      },
      warn: async (message, data) => {
        if (this.logger) {
          this.logger.WARN(message, data);
        } else {
          console.warn(`[WARN] ${message}`, data || '');
        }
      },
      error: async (message, error) => {
        if (this.logger) {
          this.logger.ERROR(message, error);
        } else {
          console.error(`[ERROR] ${message}`, error || '');
        }
      },
      logApiCallStart: async (url, options) => {
        const callId = `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        if (this.logger) {
          this.logger.DEBUG('API call started', { callId, url, options });
        } else {
          console.log(`[API] Starting call ${callId} to: ${url}`, options);
        }
        return callId;
      },
      logApiCallEnd: async (callId, response, duration) => {
        if (this.logger) {
          this.logger.DEBUG('API call completed', {
            callId,
            status: response.status,
            statusText: response.statusText,
            duration
          });
        } else {
          console.log(`[API] Call ${callId} completed in ${duration}ms - Status: ${response.status}`);
        }
      }
    };
  }

  /**
   * Shows error modal with dismiss capability
   *
   * @param {string} title - Modal title
   * @param {string} message - Error message to display
   */
  showErrorModal(title, message) {
    // Create modal overlay
    const overlay = document.createElement('div');
    overlay.className = 'error-modal-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    `;

    // Create modal content
    const modal = document.createElement('div');
    modal.className = 'error-modal';
    modal.style.cssText = `
      background: white;
      border-radius: 8px;
      padding: 24px;
      max-width: 500px;
      width: 90%;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      position: relative;
    `;

    modal.innerHTML = `
      <div style="display: flex; align-items: flex-start; gap: 16px; margin-bottom: 20px;">
        <div style="color: #dc2626; font-size: 24px; flex-shrink: 0;">⚠️</div>
        <div style="flex: 1;">
          <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 18px; font-weight: 600;">${title}</h3>
          <p style="margin: 0; color: #6b7280; line-height: 1.5;">${message}</p>
        </div>
      </div>
      <div style="display: flex; justify-content: flex-end; gap: 12px;">
        <button class="dismiss-btn" style="
          background: #dc2626;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          font-size: 14px;
        ">Dismiss</button>
      </div>
    `;

    // Add dismiss functionality
    const dismissBtn = modal.querySelector('.dismiss-btn');
    const dismiss = () => {
      document.body.removeChild(overlay);
    };

    dismissBtn.addEventListener('click', dismiss);
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) dismiss();
    });

    // Add keyboard support
    const handleKeydown = (e) => {
      if (e.key === 'Escape') {
        dismiss();
        document.removeEventListener('keydown', handleKeydown);
      }
    };
    document.addEventListener('keydown', handleKeydown);

    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // Focus the dismiss button
    dismissBtn.focus();
  }

  /**
   * Shows error message
   *
   * @param {string} message - Error message to display
   */
  showError(message) {
    this.showErrorModal('Error', message);
  }
}

// Initialize settings UI when script loads
new LimitlessSettingsUI();
