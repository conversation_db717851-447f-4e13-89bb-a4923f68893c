/**
 * Dashboard UI JavaScript
 *
 * Handles data visualization, statistics, and dashboard interactions
 * for the Limitless AI integration plugin.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

class LimitlessDashboardUI {
  /**
   * Creates a new LimitlessDashboardUI instance
   */
  constructor() {
    this.pluginAPI = null;
    this.refreshInterval = null;
    this.currentFilter = 'all';

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initialize());
    } else {
      this.initialize();
    }
  }

  /**
   * Initializes the dashboard UI
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Get plugin API reference from parent window
      this.pluginAPI = window.parent?.pluginAPI || window.pluginAPI;

      if (!this.pluginAPI) {
        console.error('Plugin API not available');
        this.showError('Plugin API not available. Please reload the plugin.');
        return;
      }

      // Set up event listeners
      this.setupEventListeners();

      // Load initial data
      await this.loadDashboardData();

      // Set up auto-refresh
      this.startAutoRefresh();

      console.log('Limitless Dashboard UI initialized successfully');

    } catch (error) {
      console.error('Failed to initialize dashboard UI:', error);
      this.showError('Failed to initialize dashboard interface.');
    }
  }

  /**
   * Sets up all event listeners for dashboard elements
   */
  setupEventListeners() {
    // Activity filter
    const activityFilter = document.getElementById('activityFilter');
    if (activityFilter) {
      activityFilter.addEventListener('change', (e) => {
        this.currentFilter = e.target.value;
        this.loadRecentActivity();
      });
    }

    // Refresh buttons
    const refreshActivity = document.getElementById('refreshActivity');
    if (refreshActivity) {
      refreshActivity.addEventListener('click', () => this.loadRecentActivity());
    }

    const refreshDashboard = document.getElementById('refreshDashboard');
    if (refreshDashboard) {
      refreshDashboard.addEventListener('click', () => this.loadDashboardData());
    }

    // Action buttons
    const syncNowBtn = document.getElementById('syncNowBtn');
    if (syncNowBtn) {
      syncNowBtn.addEventListener('click', () => this.performSync());
    }

    const viewSettingsBtn = document.getElementById('viewSettingsBtn');
    if (viewSettingsBtn) {
      viewSettingsBtn.addEventListener('click', () => this.openSettings());
    }

    // Data management buttons
    const exportDataBtn = document.getElementById('exportDataBtn');
    if (exportDataBtn) {
      exportDataBtn.addEventListener('click', () => this.exportData());
    }

    const searchDataBtn = document.getElementById('searchDataBtn');
    if (searchDataBtn) {
      searchDataBtn.addEventListener('click', () => this.searchData());
    }

    const clearDataBtn = document.getElementById('clearDataBtn');
    if (clearDataBtn) {
      clearDataBtn.addEventListener('click', () => this.clearData());
    }
  }

  /**
   * Loads all dashboard data
   *
   * @returns {Promise<void>}
   */
  async loadDashboardData() {
    try {
      // Show loading state
      this.showLoadingState();

      // Load statistics
      await this.loadStatistics();

      // Load recent activity
      await this.loadRecentActivity();

      // Load insights
      await this.loadInsights();

      // Hide loading state
      this.hideLoadingState();

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      this.showError('Failed to load dashboard data.');
      this.hideLoadingState();
    }
  }

  /**
   * Loads statistics overview
   *
   * @returns {Promise<void>}
   */
  async loadStatistics() {
    try {
      // Get stored data from plugin
      const settings = this.pluginAPI.storage.loadData() || {};
      const lifelogData = settings.lifelogData || [];
      const generatedPosts = settings.generatedPosts || [];

      // Calculate statistics
      const totalLifelogs = lifelogData.length;
      const totalPosts = generatedPosts.length;
      const lastSyncTime = settings.lastSyncTime || null;
      const totalDuration = this.calculateTotalDuration(lifelogData);

      // Update UI elements
      this.updateElement('totalLifelogs', totalLifelogs);
      this.updateElement('generatedPosts', totalPosts);
      this.updateElement('lastSyncTime', this.formatLastSyncTime(lastSyncTime));
      this.updateElement('totalDuration', this.formatDuration(totalDuration));

    } catch (error) {
      console.error('Failed to load statistics:', error);
    }
  }

  /**
   * Loads recent activity data
   *
   * @returns {Promise<void>}
   */
  async loadRecentActivity() {
    try {
      const settings = this.pluginAPI.storage.loadData() || {};
      const activityLog = settings.activityLog || [];

      // Filter activity based on current filter
      const filteredActivity = this.filterActivity(activityLog, this.currentFilter);

      // Update activity list
      this.updateActivityList(filteredActivity);

    } catch (error) {
      console.error('Failed to load recent activity:', error);
    }
  }

  /**
   * Loads data insights
   *
   * @returns {Promise<void>}
   */
  async loadInsights() {
    try {
      const settings = this.pluginAPI.storage.loadData() || {};
      const lifelogData = settings.lifelogData || [];

      if (lifelogData.length === 0) {
        this.showNoDataInsights();
        return;
      }

      // Calculate insights
      const topTopics = this.extractTopTopics(lifelogData);
      const conversationPatterns = this.analyzeConversationPatterns(lifelogData);

      // Update insights UI
      this.updateTopTopics(topTopics);
      this.updateConversationPatterns(conversationPatterns);

    } catch (error) {
      console.error('Failed to load insights:', error);
    }
  }

  /**
   * Filters activity based on time period
   *
   * @param {Array} activity - Raw activity data
   * @param {string} filter - Filter type ('all', 'today', 'week', 'month')
   * @returns {Array} Filtered activity
   */
  filterActivity(activity, filter) {
    if (filter === 'all') {
      return activity.slice(0, 20); // Show latest 20 items
    }

    const now = new Date();
    let filterDate;

    switch (filter) {
      case 'today':
        filterDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        filterDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        filterDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      default:
        return activity.slice(0, 20);
    }

    return activity
      .filter(item => new Date(item.timestamp) >= filterDate)
      .slice(0, 20);
  }

  /**
   * Updates the activity list in the UI
   *
   * @param {Array} activities - Filtered activity data
   */
  updateActivityList(activities) {
    const activityContainer = document.getElementById('recentActivity');
    if (!activityContainer) return;

    if (activities.length === 0) {
      activityContainer.innerHTML = `
        <div class="activity-item placeholder">
          <div class="activity-icon">📭</div>
          <div class="activity-content">
            <div class="activity-title">No activity found</div>
            <div class="activity-description">
              No activity matches the selected filter.
            </div>
          </div>
          <div class="activity-time">--</div>
        </div>
      `;
      return;
    }

    const activityHTML = activities.map(activity => `
      <div class="activity-item">
        <div class="activity-icon">${this.getActivityIcon(activity.type)}</div>
        <div class="activity-content">
          <div class="activity-title">${activity.title}</div>
          <div class="activity-description">${activity.description}</div>
        </div>
        <div class="activity-time">${this.formatRelativeTime(activity.timestamp)}</div>
      </div>
    `).join('');

    activityContainer.innerHTML = activityHTML;
  }

  /**
   * Extracts top conversation topics from lifelog data
   *
   * @param {Array} lifelogData - Lifelog data array
   * @returns {Array} Top topics with counts
   */
  extractTopTopics(lifelogData) {
    const topicCounts = {};

    lifelogData.forEach(lifelog => {
      const topics = lifelog.topics || lifelog.tags || [];
      topics.forEach(topic => {
        topicCounts[topic] = (topicCounts[topic] || 0) + 1;
      });
    });

    return Object.entries(topicCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([topic, count]) => ({ topic, count }));
  }

  /**
   * Analyzes conversation patterns from lifelog data
   *
   * @param {Array} lifelogData - Lifelog data array
   * @returns {Object} Conversation pattern analysis
   */
  analyzeConversationPatterns(lifelogData) {
    if (lifelogData.length === 0) {
      return {
        mostActiveHour: '--:--',
        avgDuration: '-- min',
        weeklyTotal: '-- hours'
      };
    }

    // Analyze most active hour
    const hourCounts = {};
    let totalDuration = 0;

    lifelogData.forEach(lifelog => {
      const date = new Date(lifelog.startTime || lifelog.timestamp);
      const hour = date.getHours();
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;

      if (lifelog.duration) {
        totalDuration += lifelog.duration;
      } else if (lifelog.startTime && lifelog.endTime) {
        const start = new Date(lifelog.startTime);
        const end = new Date(lifelog.endTime);
        totalDuration += (end - start) / 1000 / 60; // minutes
      }
    });

    const mostActiveHour = Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 12;

    const avgDuration = Math.round(totalDuration / lifelogData.length);

    // Calculate weekly total (last 7 days)
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const weeklyData = lifelogData.filter(lifelog =>
      new Date(lifelog.startTime || lifelog.timestamp) >= weekAgo
    );

    let weeklyDuration = 0;
    weeklyData.forEach(lifelog => {
      if (lifelog.duration) {
        weeklyDuration += lifelog.duration;
      } else if (lifelog.startTime && lifelog.endTime) {
        const start = new Date(lifelog.startTime);
        const end = new Date(lifelog.endTime);
        weeklyDuration += (end - start) / 1000 / 60; // minutes
      }
    });

    return {
      mostActiveHour: this.formatHour(parseInt(mostActiveHour)),
      avgDuration: `${avgDuration} min`,
      weeklyTotal: `${Math.round(weeklyDuration / 60)} hours`
    };
  }

  /**
   * Updates top topics display
   *
   * @param {Array} topics - Top topics array
   */
  updateTopTopics(topics) {
    const topicsContainer = document.getElementById('topTopics');
    if (!topicsContainer) return;

    if (topics.length === 0) {
      topicsContainer.innerHTML = `
        <div class="topic-item">
          <span class="topic-name">No data available</span>
          <span class="topic-count">--</span>
        </div>
      `;
      return;
    }

    const topicsHTML = topics.map(({ topic, count }) => `
      <div class="topic-item">
        <span class="topic-name">${topic}</span>
        <span class="topic-count">${count}</span>
      </div>
    `).join('');

    topicsContainer.innerHTML = topicsHTML;
  }

  /**
   * Updates conversation patterns display
   *
   * @param {Object} patterns - Conversation patterns data
   */
  updateConversationPatterns(patterns) {
    this.updateElement('mostActiveHour', patterns.mostActiveHour);
    this.updateElement('avgDuration', patterns.avgDuration);
    this.updateElement('weeklyTotal', patterns.weeklyTotal);
  }

  /**
   * Shows no data state for insights
   */
  showNoDataInsights() {
    this.updateTopTopics([]);
    this.updateConversationPatterns({
      mostActiveHour: '--:--',
      avgDuration: '-- min',
      weeklyTotal: '-- hours'
    });
  }

  /**
   * Performs manual sync
   *
   * @returns {Promise<void>}
   */
  async performSync() {
    const syncBtn = document.getElementById('syncNowBtn');

    try {
      if (syncBtn) {
        syncBtn.disabled = true;
        syncBtn.innerHTML = '<span class="btn-icon">⏳</span> Syncing...';
      }

      // Call plugin's sync functionality
      if (this.pluginAPI.commands) {
        await this.pluginAPI.commands.execute('limitless-sync-now');

        // Refresh dashboard data after sync
        await this.loadDashboardData();

        this.showSuccess('Sync completed successfully!');
      } else {
        throw new Error('Plugin commands not available');
      }

    } catch (error) {
      console.error('Sync failed:', error);
      this.showError('Sync failed. Please check your connection and try again.');

    } finally {
      if (syncBtn) {
        syncBtn.disabled = false;
        syncBtn.innerHTML = '<span class="btn-icon">🔄</span> Sync Now';
      }
    }
  }

  /**
   * Opens settings modal
   */
  async openSettings() {
    try {
      if (this.pluginAPI.commands) {
        await this.pluginAPI.commands.execute('limitless-configure');
      }
    } catch (error) {
      console.error('Failed to open settings:', error);
      this.showError('Failed to open settings.');
    }
  }

  /**
   * Exports data
   */
  async exportData() {
    try {
      const settings = this.pluginAPI.storage.loadData() || {};
      const exportData = {
        lifelogData: settings.lifelogData || [],
        generatedPosts: settings.generatedPosts || [],
        exportDate: new Date().toISOString(),
        version: '1.0.0'
      };

      // Create download
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `limitless-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);

      this.showSuccess('Data exported successfully!');

    } catch (error) {
      console.error('Export failed:', error);
      this.showError('Failed to export data.');
    }
  }

  /**
   * Opens search interface
   */
  searchData() {
    this.showInfo('Search functionality will be implemented in a future update.');
  }

  /**
   * Clears all data with confirmation
   */
  async clearData() {
    const confirmed = confirm(
      'Are you sure you want to clear all Limitless AI data? This action cannot be undone.\n\n' +
      'This will remove:\n' +
      '- All imported lifelogs\n' +
      '- All generated posts\n' +
      '- All activity history'
    );

    if (!confirmed) return;

    try {
      // Clear plugin storage
      const success = this.pluginAPI.storage.saveData({
        apiKey: this.pluginAPI.storage.loadData()?.apiKey || '', // Keep API key
        syncInterval: 6,
        autoSync: true,
        maxRecords: 1000,
        timezone: 'UTC'
      });

      if (success) {
        // Refresh dashboard
        await this.loadDashboardData();
        this.showSuccess('All data cleared successfully!');
      } else {
        throw new Error('Failed to clear data');
      }

    } catch (error) {
      console.error('Failed to clear data:', error);
      this.showError('Failed to clear data. Please try again.');
    }
  }

  /**
   * Starts auto-refresh for dashboard data
   */
  startAutoRefresh() {
    // Refresh every 5 minutes
    this.refreshInterval = setInterval(() => {
      this.loadDashboardData();
    }, 5 * 60 * 1000);
  }

  /**
   * Stops auto-refresh
   */
  stopAutoRefresh() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * Utility methods
   */

  updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = value;
    }
  }

  calculateTotalDuration(lifelogData) {
    return lifelogData.reduce((total, lifelog) => {
      if (lifelog.duration) {
        return total + lifelog.duration;
      } else if (lifelog.startTime && lifelog.endTime) {
        const start = new Date(lifelog.startTime);
        const end = new Date(lifelog.endTime);
        return total + (end - start) / 1000 / 60; // minutes
      }
      return total;
    }, 0);
  }

  formatDuration(minutes) {
    if (minutes < 60) {
      return `${Math.round(minutes)}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.round(minutes % 60);
    return `${hours}h ${remainingMinutes}m`;
  }

  formatLastSyncTime(timestamp) {
    if (!timestamp) return 'Never';

    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)} hours ago`;
    return date.toLocaleDateString();
  }

  formatRelativeTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  }

  formatHour(hour) {
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:00 ${ampm}`;
  }

  getActivityIcon(type) {
    const icons = {
      sync: '🔄',
      import: '📥',
      export: '📤',
      post: '📝',
      error: '❌',
      success: '✅',
      warning: '⚠️'
    };
    return icons[type] || '📋';
  }

  showLoadingState() {
    // Add loading indicators to key elements
    const elements = ['totalLifelogs', 'generatedPosts', 'lastSyncTime', 'totalDuration'];
    elements.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.textContent = '...';
      }
    });
  }

  hideLoadingState() {
    // Loading state is hidden when data is loaded
  }

  showSuccess(message) {
    console.log('Success:', message);
    // TODO: Implement toast notification
    alert(message);
  }

  showError(message) {
    console.error('Error:', message);
    // TODO: Implement toast notification
    alert(message);
  }

  showInfo(message) {
    console.info('Info:', message);
    // TODO: Implement toast notification
    alert(message);
  }

  /**
   * Cleanup when dashboard is closed
   */
  cleanup() {
    this.stopAutoRefresh();
  }
}

// Initialize dashboard UI when script loads
const dashboard = new LimitlessDashboardUI();

// Cleanup on window unload
window.addEventListener('beforeunload', () => {
  dashboard.cleanup();
});
