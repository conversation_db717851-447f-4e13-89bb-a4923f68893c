/**
 * Sync Manager
 *
 * Handles automatic data synchronization between Limitless AI and Lifeboard.
 * Manages scheduling, incremental syncing, error recovery, and sync status tracking.
 * Provides user controls for sync frequency and manual sync operations.
 *
 * @class SyncManager
 */

class SyncManager {
  /**
   * Creates a new SyncManager instance
   *
   * @param {Object} api - The Plugin API instance
   * @param {Object} logger - The Logger instance
   */
  constructor(api, logger) {
    this.api = api;
    this.logger = logger;
    this.syncInterval = null;
    this.isSyncing = false;
    this.lastSyncTime = null;
    this.syncHistory = [];
    this.maxHistoryEntries = 50;
  }

  /**
   * Initializes the sync manager
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      await this.logger.logLifecycle('sync-manager-init');

      // Load previous sync state
      await this.loadSyncState();

      // Start auto-sync if enabled
      const settings = this.api.storage.loadData();
      if (settings?.autoSync !== false) {
        await this.startAutoSync();
      }

      await this.logger.info('Sync manager initialized', {
        autoSyncEnabled: settings?.autoSync !== false,
        lastSyncTime: this.lastSyncTime
      });

    } catch (error) {
      await this.logger.error('Failed to initialize sync manager', error);
    }
  }

  /**
   * Starts automatic synchronization
   *
   * @returns {Promise<boolean>} Success status
   */
  async startAutoSync() {
    try {
      await this.logger.logSync('auto-sync-start');

      if (this.syncInterval) {
        clearInterval(this.syncInterval);
      }

      const settings = this.api.storage.loadData();
      const syncIntervalMs = (settings?.syncInterval || 21600) * 1000; // Default 6 hours

      // Validate API key before starting auto-sync
      if (!settings?.apiKey) {
        await this.logger.warn('Cannot start auto-sync: API key not configured');
        return false;
      }

      this.syncInterval = setInterval(async () => {
        await this.performSync();
      }, syncIntervalMs);

      await this.logger.info('Auto-sync started', {
        intervalMs: syncIntervalMs,
        intervalHours: syncIntervalMs / (1000 * 60 * 60)
      });

      return true;

    } catch (error) {
      await this.logger.error('Failed to start auto-sync', error);
      return false;
    }
  }

  /**
   * Stops automatic synchronization
   *
   * @returns {Promise<void>}
   */
  async stopAutoSync() {
    try {
      await this.logger.logSync('auto-sync-stop');

      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }

      await this.logger.info('Auto-sync stopped');

    } catch (error) {
      await this.logger.error('Failed to stop auto-sync', error);
    }
  }

  /**
   * Performs a full synchronization process
   *
   * @param {Object} options - Sync options
   * @returns {Promise<Object>} Sync result
   */
  async performSync(options = {}) {
    if (this.isSyncing) {
      await this.logger.warn('Sync already in progress, skipping');
      return { success: false, error: 'Sync already in progress' };
    }

    const syncId = this.generateSyncId();
    const startTime = Date.now();

    try {
      this.isSyncing = true;

      await this.logger.logSync('start', {
        syncId,
        manual: options.manual || false,
        lastSyncTime: this.lastSyncTime
      });

      // Load settings and validate API key
      const settings = this.api.storage.loadData();
      if (!settings?.apiKey) {
        throw new Error('API key not configured');
      }

      // Initialize API and data processor
      const LimitlessAPI = require('./limitless-api');
      const DataProcessor = require('./data-processor');

      const limitlessAPI = new LimitlessAPI(this.api, this.logger);
      const dataProcessor = new DataProcessor(this.api, this.logger);

      // Perform incremental sync
      const syncResult = await limitlessAPI.syncData(
        settings.apiKey,
        this.lastSyncTime,
        {
          timezone: settings.timezone || 'UTC',
          batchSize: 10,
          maxRecords: settings.maxRecords || 1000
        }
      );

      if (!syncResult.success) {
        throw new Error(`Data sync failed: ${syncResult.error}`);
      }

      // Process the fetched data
      const processedPosts = await dataProcessor.processLifelogs(syncResult.data);
      // AI enhancement disabled - using processed posts directly
      // const enhancedPosts = await dataProcessor.generatePosts(processedPosts);

      // Store the processed data
      const storeSuccess = await dataProcessor.storeProcessedData(processedPosts);

      if (!storeSuccess) {
        throw new Error('Failed to store processed data');
      }

      // Update sync state
      this.lastSyncTime = syncResult.syncTime;
      await this.saveSyncState();

      const duration = Date.now() - startTime;

      // Record sync history
      const historyEntry = {
        syncId,
        timestamp: new Date().toISOString(),
        success: true,
        duration,
        recordsProcessed: syncResult.totalFetched,
        postsCreated: processedPosts.length,
        manual: options.manual || false
      };

      await this.addToSyncHistory(historyEntry);

      await this.logger.logSync('complete', {
        syncId,
        duration: `${duration}ms`,
        recordsProcessed: syncResult.totalFetched,
        postsCreated: processedPosts.length
      });

      return {
        success: true,
        syncId,
        duration,
        recordsProcessed: syncResult.totalFetched,
        postsCreated: processedPosts.length,
        syncTime: this.lastSyncTime
      };

    } catch (error) {
      const duration = Date.now() - startTime;

      // Record failed sync in history
      const historyEntry = {
        syncId,
        timestamp: new Date().toISOString(),
        success: false,
        duration,
        error: error.message,
        manual: options.manual || false
      };

      await this.addToSyncHistory(historyEntry);

      await this.logger.logSync('error', {
        syncId,
        error: error.message,
        duration: `${duration}ms`
      });

      await this.logger.error('Sync failed', error, { syncId });

      return {
        success: false,
        syncId,
        error: error.message,
        duration
      };

    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Performs a manual sync triggered by user action
   *
   * @returns {Promise<Object>} Sync result
   */
  async performManualSync() {
    await this.logger.logUserAction('manual-sync-triggered');
    return this.performSync({ manual: true });
  }

  /**
   * Gets current sync status
   *
   * @returns {Object} Current sync status
   */
  getSyncStatus() {
    return {
      isSyncing: this.isSyncing,
      autoSyncEnabled: !!this.syncInterval,
      lastSyncTime: this.lastSyncTime,
      recentHistory: this.syncHistory.slice(-5) // Last 5 sync attempts
    };
  }

  /**
   * Gets detailed sync statistics
   *
   * @returns {Object} Sync statistics
   */
  getSyncStats() {
    const successful = this.syncHistory.filter(entry => entry.success);
    const failed = this.syncHistory.filter(entry => !entry.success);

    const totalRecords = successful.reduce((sum, entry) =>
      sum + (entry.recordsProcessed || 0), 0
    );

    const totalPosts = successful.reduce((sum, entry) =>
      sum + (entry.postsCreated || 0), 0
    );

    const avgDuration = successful.length > 0 ?
      successful.reduce((sum, entry) => sum + entry.duration, 0) / successful.length : 0;

    return {
      totalSyncs: this.syncHistory.length,
      successfulSyncs: successful.length,
      failedSyncs: failed.length,
      successRate: this.syncHistory.length > 0 ?
        (successful.length / this.syncHistory.length * 100).toFixed(1) : 0,
      totalRecordsProcessed: totalRecords,
      totalPostsCreated: totalPosts,
      averageDuration: Math.round(avgDuration),
      lastSyncTime: this.lastSyncTime,
      recentFailures: failed.slice(-3) // Last 3 failures for debugging
    };
  }

  /**
   * Updates sync configuration
   *
   * @param {Object} config - New sync configuration
   * @returns {Promise<boolean>} Success status
   */
  async updateSyncConfig(config) {
    try {
      await this.logger.logUserAction('sync-config-update', { config });

      const settings = this.api.storage.loadData() || {};

      // Update settings
      if (config.syncInterval !== undefined) {
        settings.syncInterval = config.syncInterval;
      }

      if (config.autoSync !== undefined) {
        settings.autoSync = config.autoSync;
      }

      if (config.maxRecords !== undefined) {
        settings.maxRecords = config.maxRecords;
      }

      if (config.timezone !== undefined) {
        settings.timezone = config.timezone;
      }

      // Save updated settings
      const success = this.api.storage.saveData(settings);

      if (success) {
        // Restart auto-sync with new settings
        if (settings.autoSync) {
          await this.stopAutoSync();
          await this.startAutoSync();
        } else {
          await this.stopAutoSync();
        }

        await this.logger.info('Sync configuration updated', { config });
        return true;
      } else {
        throw new Error('Failed to save sync configuration');
      }

    } catch (error) {
      await this.logger.error('Failed to update sync configuration', error);
      return false;
    }
  }

  /**
   * Loads sync state from storage
   *
   * @returns {Promise<void>}
   */
  async loadSyncState() {
    try {
      const data = this.api.storage.loadData();
      this.lastSyncTime = data?.lastSyncTime || null;
      this.syncHistory = data?.syncHistory || [];

      await this.logger.debug('Sync state loaded', {
        lastSyncTime: this.lastSyncTime,
        historyEntries: this.syncHistory.length
      });

    } catch (error) {
      await this.logger.error('Failed to load sync state', error);
      this.lastSyncTime = null;
      this.syncHistory = [];
    }
  }

  /**
   * Saves sync state to storage
   *
   * @returns {Promise<boolean>} Success status
   */
  async saveSyncState() {
    try {
      const data = this.api.storage.loadData() || {};
      data.lastSyncTime = this.lastSyncTime;
      data.syncHistory = this.syncHistory;

      const success = this.api.storage.saveData(data);

      if (success) {
        await this.logger.debug('Sync state saved');
      } else {
        throw new Error('Failed to save sync state');
      }

      return success;

    } catch (error) {
      await this.logger.error('Failed to save sync state', error);
      return false;
    }
  }

  /**
   * Adds an entry to sync history
   *
   * @param {Object} entry - History entry
   * @returns {Promise<void>}
   */
  async addToSyncHistory(entry) {
    this.syncHistory.push(entry);

    // Keep only the most recent entries
    if (this.syncHistory.length > this.maxHistoryEntries) {
      this.syncHistory = this.syncHistory.slice(-this.maxHistoryEntries);
    }

    await this.saveSyncState();
  }

  /**
   * Clears sync history
   *
   * @returns {Promise<boolean>} Success status
   */
  async clearSyncHistory() {
    try {
      await this.logger.logUserAction('clear-sync-history');

      this.syncHistory = [];
      const success = await this.saveSyncState();

      if (success) {
        await this.logger.info('Sync history cleared');
      }

      return success;

    } catch (error) {
      await this.logger.error('Failed to clear sync history', error);
      return false;
    }
  }

  /**
   * Generates a unique sync ID
   *
   * @returns {string} Unique sync ID
   */
  generateSyncId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Performs cleanup when plugin is disabled/unloaded
   *
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      await this.logger.logLifecycle('sync-manager-cleanup');

      await this.stopAutoSync();
      await this.saveSyncState();

      await this.logger.info('Sync manager cleanup completed');

    } catch (error) {
      await this.logger.error('Failed to cleanup sync manager', error);
    }
  }

  /**
   * Validates sync configuration
   *
   * @param {Object} config - Configuration to validate
   * @returns {Object} Validation result
   */
  validateSyncConfig(config) {
    const errors = [];

    if (config.syncInterval !== undefined) {
      if (typeof config.syncInterval !== 'number' || config.syncInterval < 60) {
        errors.push('Sync interval must be at least 60 seconds');
      }
      if (config.syncInterval > 86400) {
        errors.push('Sync interval cannot exceed 24 hours (86400 seconds)');
      }
    }

    if (config.maxRecords !== undefined) {
      if (typeof config.maxRecords !== 'number' || config.maxRecords < 1) {
        errors.push('Max records must be a positive number');
      }
      if (config.maxRecords > 10000) {
        errors.push('Max records cannot exceed 10,000');
      }
    }

    if (config.timezone !== undefined) {
      // Basic timezone validation - could be enhanced
      if (typeof config.timezone !== 'string' || config.timezone.length === 0) {
        errors.push('Timezone must be a non-empty string');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

module.exports = SyncManager;
