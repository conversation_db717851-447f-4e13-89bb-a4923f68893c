/**
 * API Key Validator - Background validation service for Limitless plugin
 *
 * This service handles asynchronous API key validation with comprehensive
 * retry logic and state management, decoupling validation from storage.
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

const { factory: createLogger } = require('../../../core/logger/CoreLogger');
const { secretManager } = require('../../../core/secretManager/SecretManager');
const EventBus = require('../../../src/EventBus');
const LimitlessAPI = require('./limitless-api');

const log = createLogger('limitless-api-validator');

/**
 * APIKeyValidator Class
 *
 * Manages background validation of API keys with retry logic and state tracking.
 * Integrates with existing LimitlessAPI validation and SecretManager storage.
 */
class APIKeyValidator {
    constructor() {
        this.validationStates = new Map(); // pluginId -> validation state
        this.retryStates = new Map(); // pluginId -> retry state
        this.initialized = false;

        log.INFO('APIKeyValidator initialized');
    }

    /**
     * Initialize the validator
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.initialized) {
            return;
        }

        try {
            // Load any persisted retry states
            await this.loadRetryStates();
            this.initialized = true;

            log.INFO('APIKeyValidator initialization complete');
        } catch (error) {
            log.ERROR('APIKeyValidator initialization failed', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * Validate API key in background with retry logic
     * @param {string} pluginId - Plugin identifier
     * @param {string} apiKey - API key to validate
     * @param {string} correlationId - Correlation ID for tracking
     * @returns {Promise<boolean>} Validation result
     */
    async validateAPIKeyBackground(pluginId, apiKey, correlationId = null) {
        const logContext = {
            pluginId,
            correlationId,
            keyLength: apiKey?.length || 0
        };

        try {
            log.INFO('Starting background API key validation', logContext);

            // Check if validation is already in progress
            if (this.isValidating(pluginId)) {
                log.WARN('Validation already in progress for plugin', logContext);
                return false;
            }

            // Set validation state
            this.setValidationState(pluginId, true);

            // Initialize retry state
            this.initializeRetryState(pluginId, correlationId);

            let validationResult = false;
            let lastError = null;

            // Retry loop with exponential backoff
            for (let attempt = 1; attempt <= 3; attempt++) {
                const attemptContext = { ...logContext, attempt };

                try {
                    log.INFO('Validation attempt', attemptContext);

                    // Use existing LimitlessAPI validation with built-in retry
                    validationResult = await LimitlessAPI.validateAPIKey(apiKey);

                    if (validationResult) {
                        log.INFO('API key validation successful', attemptContext);
                        break;
                    } else {
                        lastError = new Error('API key validation failed');
                        log.WARN('API key validation failed', attemptContext);
                    }

                } catch (error) {
                    lastError = error;
                    log.WARN('Validation attempt failed', {
                        ...attemptContext,
                        error: error.message
                    });
                }

                // Wait before retry (exponential backoff)
                if (attempt < 3) {
                    const delay = Math.pow(2, attempt) * 1000; // 2s, 4s
                    log.DEBUG('Waiting before retry', {
                        ...attemptContext,
                        delayMs: delay
                    });
                    await this.sleep(delay);
                }
            }

            // Update validation status in database
            await this.updateValidationStatus(pluginId, 'default', validationResult);

            // Emit completion event
            this.emitValidationComplete(pluginId, validationResult, correlationId, lastError);

            log.INFO('Background validation complete', {
                ...logContext,
                validationResult,
                finalError: lastError?.message
            });

            return validationResult;

        } catch (error) {
            log.ERROR('Background validation failed', {
                ...logContext,
                error: error.message,
                stack: error.stack
            });

            // Emit failure event
            this.emitValidationComplete(pluginId, false, correlationId, error);
            return false;

        } finally {
            // Clear validation state
            this.setValidationState(pluginId, false);
            this.clearRetryState(pluginId);
        }
    }

    /**
     * Trigger background validation after Phase 1 storage
     * @param {string} pluginId - Plugin identifier
     * @param {string} correlationId - Correlation ID for tracking
     * @returns {Promise<void>}
     */
    async triggerBackgroundValidation(pluginId, correlationId = null) {
        try {
            log.INFO('Triggering background validation', {
                pluginId,
                correlationId
            });

            // Retrieve stored API key
            const apiKey = await secretManager.retrieveAPIKey(pluginId, 'default');
            if (!apiKey) {
                throw new Error('No API key found for validation');
            }

            // Start background validation (non-blocking)
            this.validateAPIKeyBackground(pluginId, apiKey, correlationId)
                .catch(error => {
                    log.ERROR('Background validation promise rejected', {
                        pluginId,
                        correlationId,
                        error: error.message
                    });
                });

        } catch (error) {
            log.ERROR('Failed to trigger background validation', {
                pluginId,
                correlationId,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * Check if validation is currently in progress
     * @param {string} pluginId - Plugin identifier
     * @returns {boolean} True if validating
     */
    isValidating(pluginId) {
        return this.validationStates.get(pluginId) === true;
    }

    /**
     * Set validation state
     * @private
     */
    setValidationState(pluginId, isValidating) {
        this.validationStates.set(pluginId, isValidating);
    }

    /**
     * Initialize retry state for tracking
     * @private
     */
    initializeRetryState(pluginId, correlationId) {
        this.retryStates.set(pluginId, {
            correlationId,
            startedAt: new Date().toISOString(),
            attempts: 0
        });
    }

    /**
     * Clear retry state
     * @private
     */
    clearRetryState(pluginId) {
        this.retryStates.delete(pluginId);
    }

    /**
     * Update validation status in database
     * @private
     */
    async updateValidationStatus(pluginId, keyName, isValid) {
        try {
            await secretManager.setAPIKeyValidationStatus(pluginId, keyName, isValid);
            log.DEBUG('Validation status updated', {
                pluginId,
                keyName,
                isValid
            });
        } catch (error) {
            log.ERROR('Failed to update validation status', {
                pluginId,
                keyName,
                isValid,
                error: error.message
            });
            // Don't throw - this is not critical for validation flow
        }
    }

    /**
     * Emit validation completion event
     * @private
     */
    emitValidationComplete(pluginId, isValid, correlationId, error) {
        const eventData = {
            pluginId,
            isValid,
            correlationId,
            timestamp: new Date().toISOString(),
            error: error ? {
                message: error.message,
                code: error.code
            } : null
        };

        EventBus.emit('limitless:validation:complete', eventData);
        log.DEBUG('Validation completion event emitted', eventData);
    }

    /**
     * Load persisted retry states (for application restart recovery)
     * @private
     */
    async loadRetryStates() {
        // Implementation would load from plugin storage if needed
        // For now, start with clean state
        this.retryStates.clear();
        log.DEBUG('Retry states loaded');
    }

    /**
     * Sleep utility for retry delays
     * @private
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Cleanup method for plugin lifecycle
     */
    cleanup() {
        this.validationStates.clear();
        this.retryStates.clear();
        this.initialized = false;
        log.INFO('APIKeyValidator cleanup complete');
    }
}

// Export singleton instance
const apiKeyValidator = new APIKeyValidator();

module.exports = {
    APIKeyValidator,
    apiKeyValidator
};
