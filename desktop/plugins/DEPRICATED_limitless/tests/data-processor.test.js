/**
 * Unit Tests for Data Processor
 *
 * Tests data transformation functionality including:
 * - Lifelog to post transformation
 * - Data filtering and organization
 * - Storage operations
 * - Duration calculation and speaker extraction
 */

const DataProcessor = require('../src/data-processor');

// Mock dependencies
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  startTimer: jest.fn(() => ({ stop: jest.fn().mockResolvedValue(50) })),
  logDataProcessing: jest.fn(),
  logSync: jest.fn(),
  logLifecycle: jest.fn(),
  logUserAction: jest.fn(),
  logApiCallStart: jest.fn().mockResolvedValue('call_123'),
  logApiCallEnd: jest.fn()
};

const mockAPI = {
  manifest: { id: 'limitless' },
  storage: {
    loadData: jest.fn(),
    saveData: jest.fn()
  }
};

describe('DataProcessor', () => {
  let dataProcessor;
  const mockLifelogs = [
    {
      id: 'lifelog-1',
      title: 'Team Meeting',
      markdown: '# Team Meeting\n\nDiscussed project goals and challenges. Feeling optimistic about the roadmap.',
      startTime: '2025-01-01T09:00:00Z',
      endTime: '2025-01-01T10:00:00Z',
      contents: [
        {
          type: 'heading1',
          content: 'Team Meeting',
          speakerName: 'Alice',
          speakerIdentifier: 'user'
        },
        {
          type: 'blockquote',
          content: 'We need to focus on our key objectives',
          speakerName: 'Bob',
          speakerIdentifier: null
        }
      ]
    },
    {
      id: 'lifelog-2',
      title: 'Personal Reflection',
      markdown: 'Had a difficult day. Feeling frustrated with the lack of progress.',
      startTime: '2025-01-01T18:00:00Z',
      endTime: '2025-01-01T18:15:00Z',
      contents: [
        {
          type: 'blockquote',
          content: 'Need to find better ways to handle stress',
          speakerName: 'Alice',
          speakerIdentifier: 'user'
        }
      ]
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    dataProcessor = new DataProcessor(mockAPI, mockLogger);
  });

  describe('constructor', () => {
    test('should initialize with API and logger', () => {
      expect(dataProcessor.api).toBe(mockAPI);
      expect(dataProcessor.logger).toBe(mockLogger);
    });
  });

  describe('processLifelogs', () => {
    test('should transform lifelogs to posts successfully', async () => {
      const result = await dataProcessor.processLifelogs(mockLifelogs);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        id: 'limitless-lifelog-1',
        title: 'Team Meeting',
        content: '# Team Meeting\n\nDiscussed project goals and challenges. Feeling optimistic about the roadmap.',
        timestamp: '2025-01-01T09:00:00Z',
        tags: ['limitless', 'lifelog'],
        metadata: {
          source: 'limitless',
          originalId: 'lifelog-1',
          duration: 3600000, // 1 hour in milliseconds
          speakers: ['Alice', 'Bob']
        }
      });

      expect(mockLogger.logDataProcessing).toHaveBeenCalledWith('start', { totalLifelogs: 2 });
      expect(mockLogger.logDataProcessing).toHaveBeenCalledWith('complete', { postsCreated: 2 });
    });

    test('should handle empty lifelogs array', async () => {
      const result = await dataProcessor.processLifelogs([]);

      expect(result).toEqual([]);
      expect(mockLogger.logDataProcessing).toHaveBeenCalledWith('start', { totalLifelogs: 0 });
    });

    test('should handle processing errors gracefully', async () => {
      // Mock an error in transformation
      const invalidLifelog = { /* missing required fields */ };

      const result = await dataProcessor.processLifelogs([invalidLifelog]);

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalledWith('Failed to process lifelogs', expect.any(Error), expect.any(Object));
    });
  });

  describe('transformLifelogToPost', () => {
    test('should transform single lifelog correctly', () => {
      const lifelog = mockLifelogs[0];
      const result = dataProcessor.transformLifelogToPost(lifelog);

      expect(result.id).toBe('limitless-lifelog-1');
      expect(result.title).toBe('Team Meeting');
      expect(result.content).toBe(lifelog.markdown);
      expect(result.timestamp).toBe(lifelog.startTime);
      expect(result.tags).toContain('limitless');
      expect(result.metadata.source).toBe('limitless');
      expect(result.metadata.originalId).toBe('lifelog-1');
    });

    test('should calculate duration correctly', () => {
      const lifelog = mockLifelogs[0];
      const result = dataProcessor.transformLifelogToPost(lifelog);

      expect(result.metadata.duration).toBe(3600000); // 1 hour
    });

    test('should extract speakers correctly', () => {
      const lifelog = mockLifelogs[0];
      const result = dataProcessor.transformLifelogToPost(lifelog);

      expect(result.metadata.speakers).toEqual(['Alice', 'Bob']);
    });
  });

  describe('calculateDuration', () => {
    test('should calculate duration in milliseconds', () => {
      const start = '2025-01-01T10:00:00Z';
      const end = '2025-01-01T10:30:00Z';

      const duration = dataProcessor.calculateDuration(start, end);

      expect(duration).toBe(1800000); // 30 minutes in milliseconds
    });

    test('should handle Date objects', () => {
      const start = new Date('2025-01-01T10:00:00Z');
      const end = new Date('2025-01-01T10:15:00Z');

      const duration = dataProcessor.calculateDuration(start, end);

      expect(duration).toBe(900000); // 15 minutes in milliseconds
    });
  });

  describe('extractSpeakers', () => {
    test('should extract unique speaker names', () => {
      const contents = [
        { speakerName: 'Alice' },
        { speakerName: 'Bob' },
        { speakerName: 'Alice' }, // duplicate
        { speakerName: null },
        { speakerName: 'Charlie' }
      ];

      const speakers = dataProcessor.extractSpeakers(contents);

      expect(speakers).toEqual(['Alice', 'Bob', 'Charlie']);
    });

    test('should handle empty contents', () => {
      const speakers = dataProcessor.extractSpeakers([]);
      expect(speakers).toEqual([]);
    });

    test('should filter out null/undefined speakers', () => {
      const contents = [
        { speakerName: null },
        { speakerName: undefined },
        { speakerName: 'Alice' }
      ];

      const speakers = dataProcessor.extractSpeakers(contents);

      expect(speakers).toEqual(['Alice']);
    });
  });

  describe('filterPosts', () => {
    const samplePosts = [
      {
        id: 'post-1',
        timestamp: '2025-01-01T10:00:00Z',
        tags: ['work', 'meeting'],
        themes: ['work'],
        sentiment: 'positive',
        metadata: { duration: 1800000, speakers: ['Alice'] }
      },
      {
        id: 'post-2',
        timestamp: '2025-01-02T15:00:00Z',
        tags: ['personal', 'reflection'],
        themes: ['health'],
        sentiment: 'negative',
        metadata: { duration: 600000, speakers: ['Bob'] }
      }
    ];

    test('should filter by date range', () => {
      const filters = {
        dateRange: {
          start: '2025-01-01T00:00:00Z',
          end: '2025-01-01T23:59:59Z'
        }
      };

      const result = dataProcessor.filterPosts(samplePosts, filters);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('post-1');
    });

    test('should filter by tags', () => {
      const filters = { tags: ['work'] };

      const result = dataProcessor.filterPosts(samplePosts, filters);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('post-1');
    });

    test('should filter by themes', () => {
      const filters = { themes: ['health'] };

      const result = dataProcessor.filterPosts(samplePosts, filters);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('post-2');
    });

    test('should filter by sentiment', () => {
      const filters = { sentiment: 'positive' };

      const result = dataProcessor.filterPosts(samplePosts, filters);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('post-1');
    });

    test('should filter by duration', () => {
      const filters = { minDuration: 1000000 }; // > 16 minutes

      const result = dataProcessor.filterPosts(samplePosts, filters);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('post-1');
    });

    test('should filter by speakers', () => {
      const filters = { speakers: ['Bob'] };

      const result = dataProcessor.filterPosts(samplePosts, filters);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('post-2');
    });

    test('should combine multiple filters', () => {
      const filters = {
        sentiment: 'positive',
        tags: ['work']
      };

      const result = dataProcessor.filterPosts(samplePosts, filters);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('post-1');
    });

    test('should return empty array when no posts match', () => {
      const filters = { sentiment: 'positive', tags: ['nonexistent'] };

      const result = dataProcessor.filterPosts(samplePosts, filters);

      expect(result).toEqual([]);
    });
  });

  describe('storeProcessedData', () => {
    test('should store posts successfully', async () => {
      const posts = [{ id: 'test-post', content: 'test' }];
      mockAPI.storage.saveData.mockReturnValue(true);

      const result = await dataProcessor.storeProcessedData(posts);

      expect(result).toBe(true);
      expect(mockAPI.storage.saveData).toHaveBeenCalledWith({
        posts,
        lastUpdated: expect.any(String),
        totalCount: 1
      });
      expect(mockLogger.info).toHaveBeenCalledWith('Storing processed posts', { postCount: 1 });
    });

    test('should handle storage failure', async () => {
      const posts = [{ id: 'test-post' }];
      mockAPI.storage.saveData.mockReturnValue(false);

      const result = await dataProcessor.storeProcessedData(posts);

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith('Failed to store processed posts', expect.any(Error));
    });
  });

  describe('loadProcessedData', () => {
    test('should load posts successfully', async () => {
      const mockData = {
        posts: [{ id: 'test-post' }],
        lastUpdated: '2025-01-01T00:00:00Z'
      };
      mockAPI.storage.loadData.mockReturnValue(mockData);

      const result = await dataProcessor.loadProcessedData();

      expect(result).toEqual(mockData.posts);
      expect(mockLogger.info).toHaveBeenCalledWith('Posts loaded from storage', {
        postCount: 1,
        lastUpdated: mockData.lastUpdated
      });
    });

    test('should handle missing data gracefully', async () => {
      mockAPI.storage.loadData.mockReturnValue({});

      const result = await dataProcessor.loadProcessedData();

      expect(result).toEqual([]);
    });

    test('should handle storage errors', async () => {
      mockAPI.storage.loadData.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const result = await dataProcessor.loadProcessedData();

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalledWith('Failed to load processed posts', expect.any(Error));
    });
  });
});
