// Mock logger for testing
const mockDebugContext = {
  id: 'test-debug-context',
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  end: jest.fn()
};

const logger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  createDebugContext: jest.fn().mockReturnValue(mockDebugContext),
  logApiCallStart: jest.fn().mockResolvedValue('test-call-id'),
  logApiCallEnd: jest.fn().mockResolvedValue(),
  cleanupDebugContext: jest.fn(),
  // Add any other logger methods used in your code
};

module.exports = {
  __esModule: true,
  default: logger,
  createScopedLogger: jest.fn().mockImplementation(() => logger)
};
