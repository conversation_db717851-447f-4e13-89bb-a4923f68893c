#!/bin/bash

echo "🧪 Running Limitless Plugin Test Suite"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results summary
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

echo -e "${YELLOW}📋 Test Configuration:${NC}"
echo "  - Framework: Jest"
echo "  - Environment: Node.js"
echo "  - Coverage: Enabled"
echo ""

echo -e "${YELLOW}🔍 Running Unit Tests...${NC}"
echo ""

# Run Jest tests with coverage
npm test -- --coverage --verbose

TEST_EXIT_CODE=$?

echo ""
echo "======================================="

if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ All tests passed successfully!${NC}"
else
    echo -e "${RED}❌ Some tests failed. See output above for details.${NC}"
fi

echo ""
echo -e "${YELLOW}📊 Test Summary:${NC}"
echo "  - Exit Code: $TEST_EXIT_CODE"
echo "  - Test Files: 3 (API, Data Processor, Integration)"
echo "  - Coverage Report: Generated in coverage/ directory"
echo ""

if [ $TEST_EXIT_CODE -ne 0 ]; then
    echo -e "${YELLOW}🔧 Common Issues & Solutions:${NC}"
    echo "  1. Missing mock methods - Add to test setup"
    echo "  2. Async timing issues - Increase timeouts"
    echo "  3. Module import errors - Check require paths"
    echo "  4. Plugin initialization - Mock PluginAPI properly"
    echo ""
fi

echo -e "${YELLOW}📝 Next Steps:${NC}"
echo "  1. Fix any failing tests"
echo "  2. Improve test coverage (target: >80%)"
echo "  3. Add end-to-end tests"
echo "  4. Set up CI/CD pipeline"
echo ""

exit $TEST_EXIT_CODE
