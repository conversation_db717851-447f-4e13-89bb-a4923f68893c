{"author": "Lifeboard Team", "description": "Limitless AI integration plugin for Lifeboard", "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "babel-jest": "^29.7.0", "eslint": "^8.56.0", "jest": "^29.7.0"}, "eslintConfig": {"env": {"jest": true, "node": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "no-var": "error", "prefer-const": "error"}}, "jest": {"collectCoverageFrom": ["src/**/*.js", "main.js", "!**/node_modules/**", "!**/tests/**"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js"], "verbose": true}, "keywords": ["lifeboard", "plugin", "limitless", "ai", "lifelog"], "license": "MIT", "main": "main.js", "name": "limitless-ai-plugin", "scripts": {"lint": "eslint src/ tests/ main.js", "lint:fix": "eslint src/ tests/ main.js --fix", "test": "jest", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "test:watch": "jest --watch"}, "version": "1.0.0"}