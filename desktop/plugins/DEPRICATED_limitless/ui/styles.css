/**
 * Limitless AI Plugin Styles
 *
 * Professional styling for the Limitless AI integration plugin UI components.
 * Follows Lifeboard's design system with plugin-specific enhancements.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

/* CSS Variables for consistent theming */
:root {
  --limitless-primary: #2563eb;
  --limitless-primary-dark: #1d4ed8;
  --limitless-secondary: #64748b;
  --limitless-success: #059669;
  --limitless-warning: #d97706;
  --limitless-error: #dc2626;
  --limitless-background: #ffffff;
  --limitless-background-secondary: #f8fafc;
  --limitless-border: #e2e8f0;
  --limitless-text: #1e293b;
  --limitless-text-muted: #64748b;
  --limitless-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --limitless-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --limitless-radius: 0.5rem;
  --limitless-radius-sm: 0.25rem;
  --limitless-spacing: 1rem;
  --limitless-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  :root {
    --limitless-background: #0f172a;
    --limitless-background-secondary: #1e293b;
    --limitless-border: #334155;
    --limitless-text: #f1f5f9;
    --limitless-text-muted: #94a3b8;
  }
}

/* Base container styles */
.limitless-settings-container {
  font-family: var(--limitless-font-family);
  color: var(--limitless-text);
  background-color: var(--limitless-background);
  min-height: 100vh;
  padding: var(--limitless-spacing);
  line-height: 1.6;
}

.limitless-main-menu {
  font-family: var(--limitless-font-family);
  color: var(--limitless-text);
  padding: var(--limitless-spacing);
}

/* Header styles */
.settings-header {
  margin-bottom: calc(var(--limitless-spacing) * 2);
  padding-bottom: var(--limitless-spacing);
  border-bottom: 1px solid var(--limitless-border);
}

.settings-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--limitless-text);
}

.settings-header p {
  margin: 0;
  color: var(--limitless-text-muted);
  font-size: 1rem;
}

/* Content layout */
.settings-content {
  max-width: 800px;
  margin: 0 auto;
}

/* Section styles */
.settings-section {
  background-color: var(--limitless-background-secondary);
  border: 1px solid var(--limitless-border);
  border-radius: var(--limitless-radius);
  padding: calc(var(--limitless-spacing) * 1.5);
  margin-bottom: calc(var(--limitless-spacing) * 1.5);
  box-shadow: var(--limitless-shadow);
}

.settings-section h2 {
  margin: 0 0 var(--limitless-spacing) 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--limitless-text);
}

/* Form group styles */
.form-group {
  margin-bottom: calc(var(--limitless-spacing) * 1.25);
}

.form-group:last-child {
  margin-bottom: 0;
}

/* Label styles */
label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--limitless-text);
}

.label-text {
  display: block;
  font-weight: 500;
  color: var(--limitless-text);
}

.label-hint {
  display: block;
  font-size: 0.875rem;
  color: var(--limitless-text-muted);
  margin-top: 0.25rem;
}

.form-hint {
  font-size: 0.875rem;
  color: var(--limitless-text-muted);
  margin-top: 0.25rem;
}

/* Input styles */
input[type="text"],
input[type="password"],
input[type="number"],
select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--limitless-border);
  border-radius: var(--limitless-radius-sm);
  background-color: var(--limitless-background);
  color: var(--limitless-text);
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
select:focus {
  outline: none;
  border-color: var(--limitless-primary);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* Input with button styling */
.input-with-button {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.input-with-button input {
  flex: 1;
}

/* Range input styles */
.input-range-group {
  display: flex;
  align-items: center;
  gap: var(--limitless-spacing);
}

input[type="range"] {
  flex: 1;
  height: 0.5rem;
  background: var(--limitless-border);
  border-radius: var(--limitless-radius-sm);
  outline: none;
  -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  background: var(--limitless-primary);
  border-radius: 50%;
  cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
  width: 1.25rem;
  height: 1.25rem;
  background: var(--limitless-primary);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.range-value {
  min-width: 5rem;
  font-weight: 500;
  color: var(--limitless-text);
}

/* Checkbox styles */
input[type="checkbox"] {
  width: 1.125rem;
  height: 1.125rem;
  margin-right: 0.5rem;
  accent-color: var(--limitless-primary);
}

.checkbox-label {
  font-weight: 500;
  color: var(--limitless-text);
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--limitless-radius-sm);
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  user-select: none;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled,
.btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  pointer-events: none;
}

.btn-primary {
  background-color: var(--limitless-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--limitless-primary-dark);
}

.btn-secondary {
  background-color: var(--limitless-background);
  color: var(--limitless-text);
  border: 1px solid var(--limitless-border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--limitless-background-secondary);
}

.btn-danger {
  background-color: var(--limitless-error);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
}

.btn-icon {
  width: 1rem;
  height: 1rem;
  padding: 0.5rem;
  min-width: auto;
}

.btn-icon:hover {
  background-color: var(--limitless-background-secondary);
}

/* Status indicators */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--limitless-spacing);
}

.status-card {
  background-color: var(--limitless-background);
  border: 1px solid var(--limitless-border);
  border-radius: var(--limitless-radius-sm);
  padding: var(--limitless-spacing);
}

.status-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--limitless-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-info {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.connected {
  background-color: var(--limitless-success);
}

.status-dot.enabled {
  background-color: var(--limitless-success);
}

.status-dot.disabled {
  background-color: var(--limitless-secondary);
}

.status-dot.unknown {
  background-color: var(--limitless-warning);
}

.status-text {
  font-weight: 500;
  color: var(--limitless-text);
}

/* Action buttons */
.settings-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--limitless-spacing);
  margin-top: calc(var(--limitless-spacing) * 2);
  padding-top: var(--limitless-spacing);
  border-top: 1px solid var(--limitless-border);
}

.actions-left,
.actions-right {
  display: flex;
  gap: 0.5rem;
}

/* Validation status */
.validation-status {
  margin-top: 0.5rem;
  min-height: 1.5rem;
}

.validation-success {
  color: var(--limitless-success);
  font-weight: 500;
}

.validation-error {
  color: var(--limitless-error);
  font-weight: 500;
}

.validating {
  color: var(--limitless-primary);
  font-weight: 500;
}

/* Progress bar */
.sync-progress {
  margin-top: var(--limitless-spacing);
  padding: var(--limitless-spacing);
  background-color: var(--limitless-background-secondary);
  border: 1px solid var(--limitless-border);
  border-radius: var(--limitless-radius-sm);
}

.progress-bar {
  width: 100%;
  height: 0.5rem;
  background-color: var(--limitless-border);
  border-radius: var(--limitless-radius-sm);
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background-color: var(--limitless-primary);
  transition: width 0.3s ease;
  width: 0%;
}

.progress-text {
  font-size: 0.875rem;
  color: var(--limitless-text-muted);
  text-align: center;
}

/* Main menu specific styles */
.status-section {
  margin-bottom: calc(var(--limitless-spacing) * 1.5);
}

.status-section h3 {
  margin: 0 0 var(--limitless-spacing) 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--limitless-text);
}

.status-section p {
  margin: 0.5rem 0;
  color: var(--limitless-text);
}

.actions-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.actions-section button {
  padding: 0.75rem 1.5rem;
  background-color: var(--limitless-primary);
  color: white;
  border: none;
  border-radius: var(--limitless-radius-sm);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.actions-section button:hover {
  background-color: var(--limitless-primary-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .limitless-settings-container {
    padding: 0.5rem;
  }

  .settings-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .actions-left,
  .actions-right {
    justify-content: center;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .input-with-button {
    flex-direction: column;
  }

  .input-with-button input {
    margin-bottom: 0.5rem;
  }
}

/* Animation keyframes */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.validating::before {
  content: '';
  animation: spin 1s linear infinite;
}

/* Focus improvements for accessibility */
.btn:focus,
input:focus,
select:focus {
  outline: 2px solid var(--limitless-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --limitless-border: #000000;
    --limitless-text: #000000;
    --limitless-background: #ffffff;
  }
}

/* Dashboard-specific styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--limitless-spacing);
  margin-bottom: calc(var(--limitless-spacing) * 1.5);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--limitless-spacing);
  background-color: var(--limitless-background);
  border: 1px solid var(--limitless-border);
  border-radius: var(--limitless-radius-sm);
  padding: var(--limitless-spacing);
  box-shadow: var(--limitless-shadow);
}

.stat-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--limitless-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  display: block;
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--limitless-text);
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--limitless-text-muted);
}

/* Activity styles */
.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--limitless-spacing);
}

.activity-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.activity-filter {
  padding: 0.5rem;
  border: 1px solid var(--limitless-border);
  border-radius: var(--limitless-radius-sm);
  background-color: var(--limitless-background);
  color: var(--limitless-text);
}

.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--limitless-spacing);
  padding: 0.75rem;
  border-bottom: 1px solid var(--limitless-border);
  transition: background-color 0.2s;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover:not(.placeholder) {
  background-color: var(--limitless-background-secondary);
}

.activity-item.placeholder {
  opacity: 0.6;
  font-style: italic;
}

.activity-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-weight: 500;
  color: var(--limitless-text);
  margin-bottom: 0.25rem;
}

.activity-description {
  font-size: 0.875rem;
  color: var(--limitless-text-muted);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--limitless-text-muted);
  flex-shrink: 0;
}

/* Insights styles */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--limitless-spacing);
}

.insight-card {
  background-color: var(--limitless-background);
  border: 1px solid var(--limitless-border);
  border-radius: var(--limitless-radius-sm);
  padding: var(--limitless-spacing);
}

.insight-card h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--limitless-text);
}

.topic-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: var(--limitless-background-secondary);
  border-radius: var(--limitless-radius-sm);
}

.topic-name {
  font-weight: 500;
  color: var(--limitless-text);
}

.topic-count {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--limitless-primary);
  background-color: var(--limitless-background);
  padding: 0.25rem 0.5rem;
  border-radius: var(--limitless-radius-sm);
}

.pattern-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.pattern-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pattern-label {
  font-size: 0.875rem;
  color: var(--limitless-text-muted);
}

.pattern-value {
  font-weight: 600;
  color: var(--limitless-text);
}

/* Management styles */
.management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--limitless-spacing);
}

.management-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background-color: var(--limitless-background);
  border: 1px solid var(--limitless-border);
  border-radius: var(--limitless-radius-sm);
  padding: calc(var(--limitless-spacing) * 1.5);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.management-card:hover {
  border-color: var(--limitless-primary);
  box-shadow: var(--limitless-shadow-lg);
}

.management-card.warning {
  border-color: var(--limitless-error);
}

.management-card.warning:hover {
  border-color: var(--limitless-error);
  box-shadow: 0 10px 15px -3px rgb(220 38 38 / 0.1), 0 4px 6px -4px rgb(220 38 38 / 0.1);
}

.management-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
}

.management-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--limitless-text);
}

.management-content p {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  color: var(--limitless-text-muted);
  line-height: 1.5;
}

/* Responsive dashboard styles */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .management-grid {
    grid-template-columns: 1fr;
  }

  .activity-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .activity-controls {
    justify-content: center;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
