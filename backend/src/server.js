#!/usr/bin/env node

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Import service infrastructure
const ServiceManager = require('./services/service-manager');
const LimitlessService = require('./services/limitless-service');
const WeatherService = require('./services/weather-service');
const { Logger } = require('./utils/logger');
const { validateEnvironment } = require('./utils/env-validator');

/**
 * Lifeboard Backend Server
 * 
 * Main server application that manages all backend services
 */
class LifeboardServer {
  constructor() {
    this.app = express();
    this.server = null;
    this.serviceManager = null;
    this.logger = null;
    this.isShuttingDown = false;
    
    // Configuration
    this.config = {
      port: parseInt(process.env.PORT || '3000'),
      host: process.env.HOST || '0.0.0.0',
      environment: process.env.NODE_ENV || 'development',
      logLevel: process.env.LOG_LEVEL || 'info',
      apiPrefix: process.env.API_PREFIX || '/api/v1'
    };
  }

  /**
   * Initialize the server
   */
  async initialize() {
    try {
      // Validate environment variables
      await validateEnvironment();
      
      // Initialize logger
      this.initializeLogger();
      
      this.logger.info('Lifeboard Backend Server: Starting initialization', {
        version: process.env.npm_package_version || '1.0.0',
        environment: this.config.environment,
        port: this.config.port
      });
      
      // Setup Express middleware
      this.setupMiddleware();
      
      // Initialize service manager
      await this.initializeServiceManager();
      
      // Setup API routes
      this.setupRoutes();
      
      // Setup error handling
      this.setupErrorHandling();
      
      this.logger.info('Lifeboard Backend Server: Initialization completed');
      
    } catch (error) {
      console.error('Failed to initialize server:', error);
      process.exit(1);
    }
  }

  /**
   * Initialize logging system
   */
  initializeLogger() {
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    this.logger = new Logger({
      component: 'lifeboard-server',
      level: this.config.logLevel,
      logDir: logDir
    });
  }

  /**
   * Setup Express middleware
   */
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false // Disable CSP for API server
    }));
    
    // CORS
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || '*',
      credentials: true
    }));
    
    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP'
    });
    this.app.use(limiter);
    
    // Compression
    this.app.use(compression());
    
    // JSON parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
    // Request logging
    this.app.use((req, res, next) => {
      this.logger.debug('HTTP Request', {
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  /**
   * Initialize service manager
   */
  async initializeServiceManager() {
     this.logger.info('Initializing service manager');
     
     this.serviceManager = new ServiceManager({
       logger: this.logger,
       logDir: path.join(process.cwd(), 'logs', 'services')
     });
     
     // Set up service manager event handlers
     this.serviceManager.on('serviceError', (data) => {
       this.logger.error('Service error occurred', {
         serviceName: data.serviceName,
         error: data.error.message
       });
     });
     
     this.serviceManager.on('serviceStarted', (data) => {
       this.logger.info('Service started', {
         serviceName: data.serviceName
       });
     });
     
     this.serviceManager.on('serviceStopped', (data) => {
       this.logger.info('Service stopped', {
         serviceName: data.serviceName
       });
     });
     
     // Register services
     await this.registerServices();
     
     // Initialize and start services
     await this.serviceManager.initialize();
     await this.serviceManager.start();
     
     this.logger.info('Service manager initialized and started');
   }
 
   /**
    * Register services with the service manager
    */
   async registerServices() {
     this.logger.info('Registering services');
     
     // Register Limitless Service
     if (process.env.LIMITLESS_ENABLED === 'true') {
       const limitlessService = new LimitlessService({
         logger: this.logger,
         logDir: path.join(process.cwd(), 'logs', 'services')
       });
       
       this.serviceManager.registerService('limitless', limitlessService, {
         priority: 10,
         dependencies: []
       });
       
       this.logger.info('Registered Limitless service');
     }
     
     // Register Weather Service
     if (process.env.WEATHER_ENABLED === 'true') {
       const weatherService = new WeatherService({
         logger: this.logger,
         logDir: path.join(process.cwd(), 'logs', 'services')
       });
       
       this.serviceManager.registerService('weather', weatherService, {
         priority: 5,
         dependencies: []
       });
       
       this.logger.info('Registered Weather service');
     }
     
     this.logger.info('Service registration completed');
   }
  /**
   * Setup API routes
   */
  setupRoutes() {
    const apiRouter = express.Router();
    
    // Health check endpoint
    // Health check endpoint
    apiRouter.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        services: this.serviceManager.getStatus()
      });
    });
    
    // Service management endpoints
    apiRouter.get('/services', async (req, res) => {
      try {
        const services = this.serviceManager.getServices();
        const servicesList = {};
        
        for (const [name, service] of services) {
          servicesList[name] = service.instance.getStatus();
        }
        
        res.json({ services: servicesList });
      } catch (error) {
        this.logger.error('Failed to list services', { error: error.message });
        res.status(500).json({ error: 'Failed to list services' });
      }
    });
    
    apiRouter.get('/services/:serviceName', async (req, res) => {
      try {
        const { serviceName } = req.params;
        const service = this.serviceManager.getService(serviceName);
        
        if (!service) {
          return res.status(404).json({ error: 'Service not found' });
        }
        
        const status = service.getStatus();
        res.json({ service: status });
      } catch (error) {
        this.logger.error('Failed to get service status', { error: error.message });
        res.status(500).json({ error: 'Failed to get service status' });
      }
    });
    
    apiRouter.post('/services/:serviceName/start', async (req, res) => {
      try {
        const { serviceName } = req.params;
        const service = this.serviceManager.getService(serviceName);
        
        if (!service) {
          return res.status(404).json({ error: 'Service not found' });
        }
        
        if (service.isStarted) {
          return res.status(400).json({ error: `Service ${serviceName} is already running` });
        }
        
        await service.start();
        res.json({ message: `Service ${serviceName} started successfully` });
        
      } catch (error) {
        this.logger.error('Failed to start service', { error: error.message });
        res.status(500).json({ error: 'Failed to start service' });
      }
    });
    
    apiRouter.post('/services/:serviceName/stop', async (req, res) => {
      try {
        const { serviceName } = req.params;
        const service = this.serviceManager.getService(serviceName);
        
        if (!service) {
          return res.status(404).json({ error: 'Service not found' });
        }
        
        if (!service.isStarted) {
          return res.status(400).json({ error: `Service ${serviceName} is not running` });
        }
        
        await service.stop();
        res.json({ message: `Service ${serviceName} stopped successfully` });
        
      } catch (error) {
        this.logger.error('Failed to stop service', { error: error.message });
        res.status(500).json({ error: 'Failed to stop service' });
      }
    });
    
    apiRouter.post('/services/:serviceName/restart', async (req, res) => {
      try {
        const { serviceName } = req.params;
        const service = this.serviceManager.getService(serviceName);
        
        if (!service) {
          return res.status(404).json({ error: 'Service not found' });
        }
        
        if (service.isStarted) {
          await service.stop();
        }
        
        await service.start();
        res.json({ message: `Service ${serviceName} restarted successfully` });
        
      } catch (error) {
        this.logger.error('Failed to restart service', { error: error.message });
        res.status(500).json({ error: 'Failed to restart service' });
      }
    });
    // Service-specific endpoints
    apiRouter.post('/services/limitless/sync', async (req, res) => {
      try {
        const limitlessService = this.serviceManager.getService('limitless');
        if (!limitlessService) {
          return res.status(404).json({ error: 'Limitless service not found' });
        }
        
        const result = await limitlessService.performManualSync();
        res.json({ result });
      } catch (error) {
        this.logger.error('Failed to sync limitless service', error);
        res.status(500).json({ error: 'Failed to sync limitless service' });
      }
    });
    
    apiRouter.post('/services/weather/sync', async (req, res) => {
      try {
        const weatherService = this.serviceManager.getService('weather');
        if (!weatherService) {
          return res.status(404).json({ error: 'Weather service not found' });
        }
        
        const result = await weatherService.performManualSync();
        res.json({ result });
      } catch (error) {
        this.logger.error('Failed to sync weather service', error);
        res.status(500).json({ error: 'Failed to sync weather service' });
      }
    });
    
    apiRouter.get('/services/weather/current', async (req, res) => {
      try {
        const weatherService = this.serviceManager.getService('weather');
        if (!weatherService) {
          return res.status(404).json({ error: 'Weather service not found' });
        }
        
        const weather = await weatherService.getCurrentWeather();
        res.json({ weather });
      } catch (error) {
        this.logger.error('Failed to get current weather', error);
        res.status(500).json({ error: 'Failed to get current weather' });
      }
    });
    
    apiRouter.get('/services/weather/forecast', async (req, res) => {
      try {
        const weatherService = this.serviceManager.getService('weather');
        if (!weatherService) {
          return res.status(404).json({ error: 'Weather service not found' });
        }
        
        const days = parseInt(req.query.days) || 5;
        const forecast = await weatherService.getForecast(days);
        res.json({ forecast });
      } catch (error) {
        this.logger.error('Failed to get weather forecast', error);
        res.status(500).json({ error: 'Failed to get weather forecast' });
      }
    });
    
    // Mount API routes
    this.app.use(this.config.apiPrefix, apiRouter);
    
    // Root endpoint
    this.app.get('/', (req, res) => {
      res.json({
        name: 'Lifeboard Backend API',
        version: process.env.npm_package_version || '1.0.0',
        environment: this.config.environment,
        apiPrefix: this.config.apiPrefix,
        uptime: process.uptime()
      });
    });
  }

  /**
   * Setup error handling
   */
  setupErrorHandling() {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: 'The requested resource was not found'
      });
    });
    
    // Global error handler
    this.app.use((err, req, res, next) => {
      this.logger.error('Unhandled error', {
        error: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method
      });
      
      res.status(500).json({
        error: 'Internal Server Error',
        message: this.config.environment === 'development' ? err.message : 'Something went wrong'
      });
    });
  }

  /**
   * Start the server
   */
  async start() {
    try {
      await this.initialize();
      
      this.server = this.app.listen(this.config.port, this.config.host, () => {
        this.logger.info('Lifeboard Backend Server: Started successfully', {
          host: this.config.host,
          port: this.config.port,
          environment: this.config.environment,
          apiPrefix: this.config.apiPrefix
        });
      });
      
      // Setup graceful shutdown
      this.setupGracefulShutdown();
      
    } catch (error) {
      this.logger.error('Failed to start server', error);
      process.exit(1);
    }
  }

  /**
   * Setup graceful shutdown
   */
  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      if (this.isShuttingDown) {
        return;
      }
      
      this.isShuttingDown = true;
      this.logger.info(`Lifeboard Backend Server: Received ${signal}, starting graceful shutdown`);
      
      try {
        // Stop accepting new connections
        if (this.server) {
          this.server.close(() => {
            this.logger.info('HTTP server closed');
          });
        }
        
        // Shutdown services
        if (this.serviceManager) {
          await this.serviceManager.stop();
          await this.serviceManager.cleanup();
        }
        
        this.logger.info('Lifeboard Backend Server: Graceful shutdown completed');
        process.exit(0);
        
      } catch (error) {
        this.logger.error('Error during graceful shutdown', error);
        process.exit(1);
      }
    };
    
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught exception', error);
      process.exit(1);
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled promise rejection', {
        reason,
        promise
      });
      process.exit(1);
    });
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  const server = new LifeboardServer();
  server.start();
}

module.exports = LifeboardServer;