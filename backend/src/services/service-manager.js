const { EventEmitter } = require('events');
const { Logger } = require('../utils/logger');

/**
 * Service Manager for Backend Services
 * 
 * Manages the lifecycle of all backend services including:
 * - Service registration and discovery
 * - Service lifecycle management (initialize, start, stop, cleanup)
 * - Service dependency management
 * - Service health monitoring
 * - Configuration management
 */
class ServiceManager extends EventEmitter {
  /**
   * Creates a new ServiceManager instance
   * 
   * @param {Object} options - Service manager configuration
   * @param {string} options.logDir - Directory for logs
   * @param {Object} options.config - Global configuration
   */
  constructor(options = {}) {
    super();
    
    this.services = new Map();
    this.serviceOrder = [];
    this.dependencies = new Map();
    this.isInitialized = false;
    this.isStarted = false;
    
    // Initialize logger
    this.logger = new Logger({
      component: 'service-manager',
      logDir: options.logDir
    });
    
    // Store configuration
    this.config = options.config || {};
    
    // Service manager state
    this.status = 'stopped';
    this.startTime = null;
    this.errors = [];
    this.stats = {
      totalServices: 0,
      runningServices: 0,
      errorServices: 0,
      lastHealthCheck: null
    };
    
    // Health check interval
    this.healthCheckInterval = null;
    this.healthCheckIntervalMs = options.healthCheckIntervalMs || 30000; // 30 seconds
  }

  /**
   * Register a service with the manager
   * 
   * @param {string} serviceName - Name of the service
   * @param {BaseService} serviceInstance - Service instance
   * @param {Object} options - Service options
   * @param {string[]} options.dependencies - Service dependencies
   * @param {number} options.priority - Service priority (higher = starts first)
   * @returns {void}
   */
  registerService(serviceName, serviceInstance, options = {}) {
    if (this.services.has(serviceName)) {
      throw new Error(`Service ${serviceName} is already registered`);
    }

    // Store service
    this.services.set(serviceName, {
      instance: serviceInstance,
      name: serviceName,
      dependencies: options.dependencies || [],
      priority: options.priority || 0,
      registeredAt: new Date(),
      status: 'registered'
    });

    // Store dependencies
    if (options.dependencies && options.dependencies.length > 0) {
      this.dependencies.set(serviceName, options.dependencies);
    }

    // Update service order based on dependencies and priority
    this.updateServiceOrder();

    // Forward service events
    serviceInstance.on('error', (error) => {
      this.handleServiceError(serviceName, error);
    });

    serviceInstance.on('started', () => {
      this.handleServiceStarted(serviceName);
    });

    serviceInstance.on('stopped', () => {
      this.handleServiceStopped(serviceName);
    });

    this.stats.totalServices = this.services.size;
    this.logger.info(`Registered service: ${serviceName}`, {
      dependencies: options.dependencies,
      priority: options.priority
    });

    this.emit('serviceRegistered', { serviceName, serviceInstance });
  }

  /**
   * Unregister a service from the manager
   * 
   * @param {string} serviceName - Name of the service to unregister
   * @returns {Promise<boolean>} Whether service was found and unregistered
   */
  async unregisterService(serviceName) {
    const service = this.services.get(serviceName);
    if (!service) {
      return false;
    }

    try {
      // Stop and cleanup service if running
      if (service.instance.isStarted) {
        await service.instance.stop();
      }
      
      if (service.instance.isInitialized) {
        await service.instance.cleanup();
      }

      // Remove from collections
      this.services.delete(serviceName);
      this.dependencies.delete(serviceName);
      
      // Update service order
      this.updateServiceOrder();
      
      this.stats.totalServices = this.services.size;
      this.logger.info(`Unregistered service: ${serviceName}`);
      
      this.emit('serviceUnregistered', { serviceName });
      return true;
      
    } catch (error) {
      this.logger.error(`Failed to unregister service ${serviceName}`, { error: error.message });
      throw error;
    }
  }

  /**
   * Get a service instance by name
   * 
   * @param {string} serviceName - Name of the service
   * @returns {BaseService|null} Service instance or null if not found
   */
  getService(serviceName) {
    const service = this.services.get(serviceName);
    return service ? service.instance : null;
  }

  /**
   * Get all registered services
   * 
   * @returns {Map<string, Object>} Map of service names to service objects
   */
  getServices() {
    return new Map(this.services);
  }

  /**
   * Initialize all services
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      this.logger.info('Initializing service manager');
      
      // Initialize services in dependency order
      for (const serviceName of this.serviceOrder) {
        const service = this.services.get(serviceName);
        if (service) {
          try {
            this.logger.info(`Initializing service: ${serviceName}`);
            await service.instance.initialize();
            service.status = 'initialized';
            
          } catch (error) {
            service.status = 'error';
            this.errors.push({
              timestamp: new Date(),
              serviceName,
              phase: 'initialization',
              error: error.message
            });
            
            this.logger.error(`Failed to initialize service ${serviceName}`, { error: error.message });
            
            // Continue with other services unless critical
            if (service.critical) {
              throw error;
            }
          }
        }
      }
      
      this.isInitialized = true;
      this.status = 'initialized';
      
      this.logger.info('Service manager initialized successfully');
      this.emit('initialized');
      
    } catch (error) {
      this.status = 'error';
      this.logger.error('Failed to initialize service manager', { error: error.message });
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Start all services
   * 
   * @returns {Promise<void>}
   */
  async start() {
    if (!this.isInitialized) {
      throw new Error('Service manager not initialized');
    }
    
    if (this.isStarted) {
      return;
    }

    try {
      this.logger.info('Starting service manager');
      
      // Start services in dependency order
      for (const serviceName of this.serviceOrder) {
        const service = this.services.get(serviceName);
        if (service && service.status === 'initialized') {
          try {
            this.logger.info(`Starting service: ${serviceName}`);
            await service.instance.start();
            service.status = 'running';
            
          } catch (error) {
            service.status = 'error';
            this.errors.push({
              timestamp: new Date(),
              serviceName,
              phase: 'start',
              error: error.message
            });
            
            this.logger.error(`Failed to start service ${serviceName}`, { error: error.message });
            
            // Continue with other services unless critical
            if (service.critical) {
              throw error;
            }
          }
        }
      }
      
      // Start health monitoring
      this.startHealthMonitoring();
      
      this.isStarted = true;
      this.status = 'running';
      this.startTime = new Date();
      
      this.updateStats();
      this.logger.info('Service manager started successfully');
      this.emit('started');
      
    } catch (error) {
      this.status = 'error';
      this.logger.error('Failed to start service manager', { error: error.message });
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Stop all services
   * 
   * @returns {Promise<void>}
   */
  async stop() {
    if (!this.isStarted) {
      return;
    }

    try {
      this.logger.info('Stopping service manager');
      
      // Stop health monitoring
      this.stopHealthMonitoring();
      
      // Stop services in reverse dependency order
      const reverseOrder = [...this.serviceOrder].reverse();
      
      for (const serviceName of reverseOrder) {
        const service = this.services.get(serviceName);
        if (service && service.instance.isStarted) {
          try {
            this.logger.info(`Stopping service: ${serviceName}`);
            await service.instance.stop();
            service.status = 'stopped';
            
          } catch (error) {
            service.status = 'error';
            this.errors.push({
              timestamp: new Date(),
              serviceName,
              phase: 'stop',
              error: error.message
            });
            
            this.logger.error(`Failed to stop service ${serviceName}`, { error: error.message });
          }
        }
      }
      
      this.isStarted = false;
      this.status = 'stopped';
      
      this.updateStats();
      this.logger.info('Service manager stopped successfully');
      this.emit('stopped');
      
    } catch (error) {
      this.status = 'error';
      this.logger.error('Failed to stop service manager', { error: error.message });
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Cleanup all services
   * 
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      this.logger.info('Cleaning up service manager');
      
      // Stop services if running
      if (this.isStarted) {
        await this.stop();
      }
      
      // Cleanup services in reverse dependency order
      const reverseOrder = [...this.serviceOrder].reverse();
      
      for (const serviceName of reverseOrder) {
        const service = this.services.get(serviceName);
        if (service && service.instance.isInitialized) {
          try {
            this.logger.info(`Cleaning up service: ${serviceName}`);
            await service.instance.cleanup();
            service.status = 'cleaned';
            
          } catch (error) {
            service.status = 'error';
            this.logger.error(`Failed to cleanup service ${serviceName}`, { error: error.message });
          }
        }
      }
      
      this.isInitialized = false;
      this.status = 'cleaned';
      
      this.logger.info('Service manager cleanup completed');
      this.emit('cleanup');
      
    } catch (error) {
      this.logger.error('Failed to cleanup service manager', { error: error.message });
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Update service order based on dependencies and priority
   * 
   * @returns {void}
   */
  updateServiceOrder() {
    const services = Array.from(this.services.values());
    
    // Sort by priority first (higher priority first)
    services.sort((a, b) => b.priority - a.priority);
    
    // Then apply topological sorting for dependencies
    const visited = new Set();
    const visiting = new Set();
    const result = [];
    
    const visit = (serviceName) => {
      if (visiting.has(serviceName)) {
        throw new Error(`Circular dependency detected involving ${serviceName}`);
      }
      
      if (visited.has(serviceName)) {
        return;
      }
      
      visiting.add(serviceName);
      
      const dependencies = this.dependencies.get(serviceName) || [];
      for (const dependency of dependencies) {
        if (!this.services.has(dependency)) {
          throw new Error(`Missing dependency: ${dependency} for service ${serviceName}`);
        }
        visit(dependency);
      }
      
      visiting.delete(serviceName);
      visited.add(serviceName);
      result.push(serviceName);
    };
    
    for (const service of services) {
      if (!visited.has(service.name)) {
        visit(service.name);
      }
    }
    
    this.serviceOrder = result;
  }

  /**
   * Start health monitoring
   * 
   * @returns {void}
   */
  startHealthMonitoring() {
    if (this.healthCheckInterval) {
      return;
    }
    
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.healthCheckIntervalMs);
    
    this.logger.debug('Started health monitoring');
  }

  /**
   * Stop health monitoring
   * 
   * @returns {void}
   */
  stopHealthMonitoring() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      this.logger.debug('Stopped health monitoring');
    }
  }

  /**
   * Perform health check on all services
   * 
   * @returns {void}
   */
  performHealthCheck() {
    this.stats.lastHealthCheck = new Date();
    
    for (const [serviceName, service] of this.services) {
      const status = service.instance.getStatus();
      
      if (status.status === 'error' || status.errors.length > 0) {
        this.logger.warn(`Health check: Service ${serviceName} has errors`, {
          status: status.status,
          errorCount: status.errors.length
        });
      }
    }
    
    this.updateStats();
    this.emit('healthCheck', this.getStatus());
  }

  /**
   * Handle service error
   * 
   * @param {string} serviceName - Name of the service
   * @param {Error} error - Error that occurred
   * @returns {void}
   */
  handleServiceError(serviceName, error) {
    this.errors.push({
      timestamp: new Date(),
      serviceName,
      phase: 'runtime',
      error: error.message
    });
    
    this.logger.error(`Service ${serviceName} error`, { error: error.message });
    this.emit('serviceError', { serviceName, error });
  }

  /**
   * Handle service started
   * 
   * @param {string} serviceName - Name of the service
   * @returns {void}
   */
  handleServiceStarted(serviceName) {
    this.updateStats();
    this.emit('serviceStarted', { serviceName });
  }

  /**
   * Handle service stopped
   * 
   * @param {string} serviceName - Name of the service
   * @returns {void}
   */
  handleServiceStopped(serviceName) {
    this.updateStats();
    this.emit('serviceStopped', { serviceName });
  }

  /**
   * Update service statistics
   * 
   * @returns {void}
   */
  updateStats() {
    let runningServices = 0;
    let errorServices = 0;
    
    for (const [, service] of this.services) {
      if (service.instance.isStarted) {
        runningServices++;
      }
      if (service.status === 'error') {
        errorServices++;
      }
    }
    
    this.stats.runningServices = runningServices;
    this.stats.errorServices = errorServices;
  }

  /**
   * Get service manager status
   * 
   * @returns {Object} Service manager status
   */
  getStatus() {
    const services = {};
    
    for (const [serviceName, service] of this.services) {
      services[serviceName] = {
        ...service.instance.getStatus(),
        registeredAt: service.registeredAt,
        dependencies: service.dependencies,
        priority: service.priority
      };
    }
    
    return {
      status: this.status,
      isInitialized: this.isInitialized,
      isStarted: this.isStarted,
      startTime: this.startTime,
      uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0,
      serviceOrder: this.serviceOrder,
      stats: { ...this.stats },
      errors: this.errors.slice(-10), // Last 10 errors
      services
    };
  }
}

module.exports = ServiceManager;