const BaseService = require('./base-service');
const { createClient } = require('@supabase/supabase-js');
const { validateEnv } = require('../utils/env-validator');

/**
 * Limitless AI Service
 * 
 * Integrates with Limitless AI to automatically capture and store lifelog data
 * including web browsing, application usage, and system activities
 */
class LimitlessService extends BaseService {
  /**
   * Creates a new LimitlessService instance
   * 
   * @param {Object} options - Service configuration options
   */
  constructor(options = {}) {
    super('limitless', options);
    
    this.supabase = null;
    this.isCapturing = false;
    this.captureInterval = null;
    this.lastCaptureTime = null;
    this.captureStats = {
      totalCaptures: 0,
      successfulCaptures: 0,
      failedCaptures: 0,
      lastSuccessTime: null,
      lastFailureTime: null
    };
  }

  /**
   * Load configuration from environment variables
   * 
   * @returns {Promise<void>}
   */
  async loadConfiguration() {
    await super.loadConfiguration();
    
    this.config = {
      ...this.config,
      enabled: process.env.LIMITLESS_ENABLED === 'true',
      apiKey: process.env.LIMITLESS_API_KEY,
      apiUrl: process.env.LIMITLESS_API_URL || 'https://api.limitless.ai',
      captureInterval: parseInt(process.env.LIMITLESS_CAPTURE_INTERVAL) || 60000, // 1 minute
      batchSize: parseInt(process.env.LIMITLESS_BATCH_SIZE) || 10,
      retryAttempts: parseInt(process.env.LIMITLESS_RETRY_ATTEMPTS) || 3,
      retryDelay: parseInt(process.env.LIMITLESS_RETRY_DELAY) || 5000,
      maxRetainDays: parseInt(process.env.LIMITLESS_MAX_RETAIN_DAYS) || 90,
      captureTypes: {
        webBrowsing: process.env.LIMITLESS_CAPTURE_WEB_BROWSING === 'true',
        applications: process.env.LIMITLESS_CAPTURE_APPLICATIONS === 'true',
        systemEvents: process.env.LIMITLESS_CAPTURE_SYSTEM_EVENTS === 'true',
        screenshots: process.env.LIMITLESS_CAPTURE_SCREENSHOTS === 'true'
      },
      privacy: {
        excludeUrls: process.env.LIMITLESS_EXCLUDE_URLS ? process.env.LIMITLESS_EXCLUDE_URLS.split(',') : [],
        excludeApps: process.env.LIMITLESS_EXCLUDE_APPS ? process.env.LIMITLESS_EXCLUDE_APPS.split(',') : [],
        enablePrivacyMode: process.env.LIMITLESS_PRIVACY_MODE === 'true'
      }
    };
  }

  /**
   * Validate service configuration
   * 
   * @returns {Promise<void>}
   */
  async validateConfiguration() {
    await super.validateConfiguration();
    
    const validationErrors = [];
    
    if (!this.config.apiKey) {
      validationErrors.push('LIMITLESS_API_KEY is required');
    }
    
    if (!this.config.apiUrl) {
      validationErrors.push('LIMITLESS_API_URL is required');
    }
    
    if (this.config.captureInterval < 10000) {
      validationErrors.push('LIMITLESS_CAPTURE_INTERVAL must be at least 10000ms (10 seconds)');
    }
    
    if (this.config.batchSize < 1 || this.config.batchSize > 100) {
      validationErrors.push('LIMITLESS_BATCH_SIZE must be between 1 and 100');
    }
    
    if (validationErrors.length > 0) {
      throw new Error(`Limitless service configuration errors: ${validationErrors.join(', ')}`);
    }
  }

  /**
   * Initialize database connections
   * 
   * @returns {Promise<void>}
   */
  async initializeDatabase() {
    await super.initializeDatabase();
    
    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration is required for Limitless service');
    }
    
    this.supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test database connection
    try {
      const { data, error } = await this.supabase
        .from('lifelog_entries')
        .select('count')
        .limit(1);
      
      if (error) {
        throw error;
      }
      
      this.logger.debug('Database connection established successfully');
      
    } catch (error) {
      this.logger.error('Failed to establish database connection', { error: error.message });
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }

  /**
   * Service-specific initialization
   * 
   * @returns {Promise<void>}
   */
  async onInitialize() {
    await super.onInitialize();
    
    // Initialize Limitless AI client
    this.limitlessClient = this.createLimitlessClient();
    
    // Test Limitless AI connection
    try {
      await this.testLimitlessConnection();
      this.logger.info('Limitless AI connection established successfully');
      
    } catch (error) {
      this.logger.error('Failed to connect to Limitless AI', { error: error.message });
      throw new Error(`Limitless AI connection failed: ${error.message}`);
    }
    
    // Initialize capture statistics
    await this.loadCaptureStats();
  }

  /**
   * Service-specific start logic
   * 
   * @returns {Promise<void>}
   */
  async onStart() {
    await super.onStart();
    
    // Start data capture
    await this.startDataCapture();
    
    // Schedule cleanup task
    this.scheduleTask(
      'cleanup',
      () => this.cleanupOldEntries(),
      24 * 60 * 60 * 1000 // Daily cleanup
    );
    
    // Schedule stats update
    this.scheduleTask(
      'stats',
      () => this.updateCaptureStats(),
      5 * 60 * 1000 // Every 5 minutes
    );
  }

  /**
   * Service-specific stop logic
   * 
   * @returns {Promise<void>}
   */
  async onStop() {
    await super.onStop();
    
    // Stop data capture
    await this.stopDataCapture();
    
    // Save final statistics
    await this.saveCaptureStats();
  }

  /**
   * Service-specific cleanup logic
   * 
   * @returns {Promise<void>}
   */
  async onCleanup() {
    await super.onCleanup();
    
    // Close Limitless client connections
    if (this.limitlessClient) {
      await this.limitlessClient.close();
      this.limitlessClient = null;
    }
  }

  /**
   * Create Limitless AI client
   * 
   * @returns {Object} Limitless client instance
   */
  createLimitlessClient() {
    // Mock Limitless client for now - replace with actual SDK when available
    return {
      async getScreenData() {
        // This would integrate with the actual Limitless AI SDK
        return {
          timestamp: new Date().toISOString(),
          screenContent: 'Mock screen content',
          activeApplication: 'Mock Application',
          url: 'https://example.com',
          title: 'Example Page'
        };
      },
      
      async close() {
        // Clean up client resources
      }
    };
  }

  /**
   * Test Limitless AI connection
   * 
   * @returns {Promise<void>}
   */
  async testLimitlessConnection() {
    // This would test the actual Limitless AI connection
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Start data capture
   * 
   * @returns {Promise<void>}
   */
  async startDataCapture() {
    if (this.isCapturing) {
      return;
    }
    
    this.isCapturing = true;
    this.captureInterval = setInterval(async () => {
      await this.captureLifelogData();
    }, this.config.captureInterval);
    
    this.logger.info('Started lifelog data capture', {
      interval: this.config.captureInterval,
      captureTypes: this.config.captureTypes
    });
  }

  /**
   * Stop data capture
   * 
   * @returns {Promise<void>}
   */
  async stopDataCapture() {
    if (!this.isCapturing) {
      return;
    }
    
    this.isCapturing = false;
    
    if (this.captureInterval) {
      clearInterval(this.captureInterval);
      this.captureInterval = null;
    }
    
    this.logger.info('Stopped lifelog data capture');
  }

  /**
   * Capture lifelog data
   * 
   * @returns {Promise<void>}
   */
  async captureLifelogData() {
    if (!this.isCapturing) {
      return;
    }
    
    try {
      this.captureStats.totalCaptures++;
      this.lastCaptureTime = new Date();
      
      // Get screen data from Limitless AI
      const screenData = await this.limitlessClient.getScreenData();
      
      // Process and filter data based on privacy settings
      const processedData = await this.processScreenData(screenData);
      
      if (processedData) {
        // Store in database
        await this.storeLifelogEntry(processedData);
        
        this.captureStats.successfulCaptures++;
        this.captureStats.lastSuccessTime = new Date();
        
        this.logger.debug('Lifelog data captured successfully', {
          type: processedData.type,
          timestamp: processedData.timestamp
        });
      }
      
    } catch (error) {
      this.captureStats.failedCaptures++;
      this.captureStats.lastFailureTime = new Date();
      
      this.logger.error('Failed to capture lifelog data', { error: error.message });
      
      // Don't throw error to avoid stopping the capture process
    }
  }

  /**
   * Process screen data based on privacy settings
   * 
   * @param {Object} screenData - Raw screen data from Limitless AI
   * @returns {Promise<Object|null>} Processed data or null if filtered out
   */
  async processScreenData(screenData) {
    if (!screenData) {
      return null;
    }
    
    // Apply privacy filters
    if (this.config.privacy.enablePrivacyMode) {
      // Check URL exclusions
      if (screenData.url && this.config.privacy.excludeUrls.some(url => screenData.url.includes(url))) {
        return null;
      }
      
      // Check app exclusions
      if (screenData.activeApplication && this.config.privacy.excludeApps.some(app => 
        screenData.activeApplication.toLowerCase().includes(app.toLowerCase()))) {
        return null;
      }
    }
    
    // Determine entry type
    let entryType = 'unknown';
    if (screenData.url) {
      entryType = 'web_browsing';
    } else if (screenData.activeApplication) {
      entryType = 'application';
    }
    
    // Skip if capture type is disabled
    if (!this.config.captureTypes.webBrowsing && entryType === 'web_browsing') {
      return null;
    }
    
    if (!this.config.captureTypes.applications && entryType === 'application') {
      return null;
    }
    
    return {
      type: entryType,
      timestamp: screenData.timestamp,
      title: screenData.title,
      url: screenData.url,
      application: screenData.activeApplication,
      content: screenData.screenContent,
      metadata: {
        captureMethod: 'limitless_ai',
        privacyMode: this.config.privacy.enablePrivacyMode
      }
    };
  }

  /**
   * Store lifelog entry in database
   * 
   * @param {Object} entryData - Processed lifelog entry data
   * @returns {Promise<void>}
   */
  async storeLifelogEntry(entryData) {
    const { data, error } = await this.supabase
      .from('lifelog_entries')
      .insert([{
        type: entryData.type,
        timestamp: entryData.timestamp,
        title: entryData.title,
        url: entryData.url,
        application: entryData.application,
        content: entryData.content,
        metadata: entryData.metadata,
        created_at: new Date().toISOString()
      }]);
    
    if (error) {
      throw new Error(`Failed to store lifelog entry: ${error.message}`);
    }
  }

  /**
   * Load capture statistics from database
   * 
   * @returns {Promise<void>}
   */
  async loadCaptureStats() {
    try {
      const { data, error } = await this.supabase
        .from('service_metadata')
        .select('metadata')
        .eq('service_name', 'limitless')
        .eq('key', 'capture_stats')
        .single();
      
      if (error && error.code !== 'PGRST116') { // Not found error
        throw error;
      }
      
      if (data && data.metadata) {
        this.captureStats = { ...this.captureStats, ...data.metadata };
      }
      
    } catch (error) {
      this.logger.warn('Failed to load capture statistics', { error: error.message });
    }
  }

  /**
   * Save capture statistics to database
   * 
   * @returns {Promise<void>}
   */
  async saveCaptureStats() {
    try {
      const { error } = await this.supabase
        .from('service_metadata')
        .upsert([{
          service_name: 'limitless',
          key: 'capture_stats',
          metadata: this.captureStats,
          updated_at: new Date().toISOString()
        }]);
      
      if (error) {
        throw error;
      }
      
    } catch (error) {
      this.logger.warn('Failed to save capture statistics', { error: error.message });
    }
  }

  /**
   * Update capture statistics
   * 
   * @returns {Promise<void>}
   */
  async updateCaptureStats() {
    await this.saveCaptureStats();
    
    // Update service stats
    this.updateStats({
      operationsCount: this.captureStats.totalCaptures,
      errorsCount: this.captureStats.failedCaptures,
      lastOperationTime: this.captureStats.lastSuccessTime
    });
  }

  /**
   * Clean up old lifelog entries
   * 
   * @returns {Promise<void>}
   */
  async cleanupOldEntries() {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.maxRetainDays);
      
      const { data, error } = await this.supabase
        .from('lifelog_entries')
        .delete()
        .lt('created_at', cutoffDate.toISOString());
      
      if (error) {
        throw error;
      }
      
      const deletedCount = data ? data.length : 0;
      this.logger.info(`Cleaned up ${deletedCount} old lifelog entries`, {
        cutoffDate: cutoffDate.toISOString(),
        maxRetainDays: this.config.maxRetainDays
      });
      
    } catch (error) {
      this.logger.error('Failed to cleanup old entries', { error: error.message });
    }
  }

  /**
   * Get service-specific status
   * 
   * @returns {Object} Enhanced status information
   */
  getStatus() {
    const baseStatus = super.getStatus();
    
    return {
      ...baseStatus,
      isCapturing: this.isCapturing,
      lastCaptureTime: this.lastCaptureTime,
      captureStats: { ...this.captureStats },
      captureTypes: this.config.captureTypes,
      privacy: {
        privacyMode: this.config.privacy.enablePrivacyMode,
        excludeUrlsCount: this.config.privacy.excludeUrls.length,
        excludeAppsCount: this.config.privacy.excludeApps.length
      }
    };
  }
}

module.exports = LimitlessService;