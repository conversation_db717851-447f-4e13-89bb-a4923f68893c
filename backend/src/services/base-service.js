const { EventEmitter } = require('events');
const { Logger } = require('../utils/logger');

/**
 * Base Service Class for Backend Services
 * 
 * Provides common functionality for all backend services including:
 * - Lifecycle management
 * - Configuration management from environment variables
 * - Database access via Supabase
 * - Logging
 * - Event emission
 * - Scheduling capabilities
 */
class BaseService extends EventEmitter {
  /**
   * Creates a new BaseService instance
   * 
   * @param {string} serviceName - Name of the service
   * @param {Object} options - Service configuration options
   * @param {Logger} options.logger - Logger instance
   * @param {string} options.logDir - Directory for service logs
   * @param {Object} options.config - Service configuration
   */
  constructor(serviceName, options = {}) {
    super();
    
    this.serviceName = serviceName;
    this.isInitialized = false;
    this.isStarted = false;
    this.scheduledTasks = new Map();
    
    // Initialize logger
    this.logger = options.logger || new Logger({
      component: `service-${serviceName}`,
      logDir: options.logDir
    });
    
    // Store configuration
    this.config = options.config || {};
    
    // Service state
    this.status = 'stopped';
    this.startTime = null;
    this.lastActivity = null;
    this.errors = [];
    this.stats = {
      operationsCount: 0,
      errorsCount: 0,
      lastOperationTime: null
    };
  }

  /**
   * Initialize the service
   * Override this method in child classes
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      this.logger.info(`Initializing ${this.serviceName} service`);
      
      // Load configuration from environment
      await this.loadConfiguration();
      
      // Validate configuration
      await this.validateConfiguration();
      
      // Initialize database connections if needed
      await this.initializeDatabase();
      
      // Run service-specific initialization
      await this.onInitialize();
      
      this.isInitialized = true;
      this.status = 'initialized';
      
      this.logger.info(`${this.serviceName} service initialized successfully`);
      this.emit('initialized');
      
    } catch (error) {
      this.status = 'error';
      this.errors.push({
        timestamp: new Date(),
        phase: 'initialization',
        error: error.message
      });
      
      this.logger.error(`Failed to initialize ${this.serviceName} service`, { error: error.message });
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Start the service
   * 
   * @returns {Promise<void>}
   */
  async start() {
    if (!this.isInitialized) {
      throw new Error(`Cannot start ${this.serviceName} - service not initialized`);
    }
    
    if (this.isStarted) {
      return;
    }

    try {
      this.logger.info(`Starting ${this.serviceName} service`);
      
      // Start service-specific functionality
      await this.onStart();
      
      // Start scheduled tasks
      await this.startScheduledTasks();
      
      this.isStarted = true;
      this.status = 'running';
      this.startTime = new Date();
      
      this.logger.info(`${this.serviceName} service started successfully`);
      this.emit('started');
      
    } catch (error) {
      this.status = 'error';
      this.errors.push({
        timestamp: new Date(),
        phase: 'start',
        error: error.message
      });
      
      this.logger.error(`Failed to start ${this.serviceName} service`, { error: error.message });
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Stop the service
   * 
   * @returns {Promise<void>}
   */
  async stop() {
    if (!this.isStarted) {
      return;
    }

    try {
      this.logger.info(`Stopping ${this.serviceName} service`);
      
      // Stop scheduled tasks
      await this.stopScheduledTasks();
      
      // Stop service-specific functionality
      await this.onStop();
      
      this.isStarted = false;
      this.status = 'stopped';
      
      this.logger.info(`${this.serviceName} service stopped successfully`);
      this.emit('stopped');
      
    } catch (error) {
      this.status = 'error';
      this.errors.push({
        timestamp: new Date(),
        phase: 'stop',
        error: error.message
      });
      
      this.logger.error(`Failed to stop ${this.serviceName} service`, { error: error.message });
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Cleanup service resources
   * 
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      this.logger.info(`Cleaning up ${this.serviceName} service`);
      
      // Stop the service if running
      if (this.isStarted) {
        await this.stop();
      }
      
      // Run service-specific cleanup
      await this.onCleanup();
      
      // Clear scheduled tasks
      this.scheduledTasks.clear();
      
      // Reset state
      this.isInitialized = false;
      this.status = 'cleaned';
      
      this.logger.info(`${this.serviceName} service cleanup completed`);
      this.emit('cleanup');
      
    } catch (error) {
      this.logger.error(`Failed to cleanup ${this.serviceName} service`, { error: error.message });
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Load configuration from environment variables
   * Override this method in child classes
   * 
   * @returns {Promise<void>}
   */
  async loadConfiguration() {
    // Default implementation - override in child classes
    this.config = {
      ...this.config,
      enabled: process.env[`${this.serviceName.toUpperCase()}_ENABLED`] === 'true',
      logLevel: process.env[`${this.serviceName.toUpperCase()}_LOG_LEVEL`] || 'info'
    };
  }

  /**
   * Validate service configuration
   * Override this method in child classes
   * 
   * @returns {Promise<void>}
   */
  async validateConfiguration() {
    // Default implementation - override in child classes
    if (!this.config.enabled) {
      throw new Error(`${this.serviceName} service is disabled`);
    }
  }

  /**
   * Initialize database connections
   * Override this method in child classes if needed
   * 
   * @returns {Promise<void>}
   */
  async initializeDatabase() {
    // Default implementation - override in child classes if needed
  }

  /**
   * Service-specific initialization
   * Override this method in child classes
   * 
   * @returns {Promise<void>}
   */
  async onInitialize() {
    // Default implementation - override in child classes
  }

  /**
   * Service-specific start logic
   * Override this method in child classes
   * 
   * @returns {Promise<void>}
   */
  async onStart() {
    // Default implementation - override in child classes
  }

  /**
   * Service-specific stop logic
   * Override this method in child classes
   * 
   * @returns {Promise<void>}
   */
  async onStop() {
    // Default implementation - override in child classes
  }

  /**
   * Service-specific cleanup logic
   * Override this method in child classes
   * 
   * @returns {Promise<void>}
   */
  async onCleanup() {
    // Default implementation - override in child classes
  }

  /**
   * Schedule a recurring task
   * 
   * @param {string} taskName - Name of the task
   * @param {Function} taskFunction - Function to execute
   * @param {number} intervalMs - Interval in milliseconds
   * @returns {string} Task ID
   */
  scheduleTask(taskName, taskFunction, intervalMs) {
    const taskId = `${this.serviceName}_${taskName}_${Date.now()}`;
    
    const intervalId = setInterval(async () => {
      try {
        this.lastActivity = new Date();
        this.stats.operationsCount++;
        this.stats.lastOperationTime = new Date();
        
        await taskFunction();
        
        this.logger.debug(`Scheduled task ${taskName} completed successfully`);
        this.emit('taskCompleted', { taskName, taskId });
        
      } catch (error) {
        this.stats.errorsCount++;
        this.errors.push({
          timestamp: new Date(),
          phase: 'scheduled_task',
          taskName,
          error: error.message
        });
        
        this.logger.error(`Scheduled task ${taskName} failed`, { error: error.message });
        this.emit('taskError', { taskName, taskId, error });
      }
    }, intervalMs);
    
    this.scheduledTasks.set(taskId, {
      taskName,
      intervalId,
      intervalMs,
      createdAt: new Date()
    });
    
    return taskId;
  }

  /**
   * Start all scheduled tasks
   * 
   * @returns {Promise<void>}
   */
  async startScheduledTasks() {
    // Tasks are started when they are scheduled
    this.logger.debug(`Started ${this.scheduledTasks.size} scheduled tasks`);
  }

  /**
   * Stop all scheduled tasks
   * 
   * @returns {Promise<void>}
   */
  async stopScheduledTasks() {
    for (const [taskId, task] of this.scheduledTasks) {
      clearInterval(task.intervalId);
    }
    
    this.logger.debug(`Stopped ${this.scheduledTasks.size} scheduled tasks`);
  }

  /**
   * Remove a scheduled task
   * 
   * @param {string} taskId - Task ID to remove
   * @returns {boolean} Whether task was found and removed
   */
  removeScheduledTask(taskId) {
    const task = this.scheduledTasks.get(taskId);
    if (task) {
      clearInterval(task.intervalId);
      this.scheduledTasks.delete(taskId);
      return true;
    }
    return false;
  }

  /**
   * Get service status
   * 
   * @returns {Object} Service status information
   */
  getStatus() {
    return {
      serviceName: this.serviceName,
      status: this.status,
      isInitialized: this.isInitialized,
      isStarted: this.isStarted,
      startTime: this.startTime,
      lastActivity: this.lastActivity,
      uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0,
      scheduledTasks: Array.from(this.scheduledTasks.keys()),
      stats: { ...this.stats },
      errors: this.errors.slice(-5), // Last 5 errors
      config: this.getSafeConfig()
    };
  }

  /**
   * Get configuration without sensitive data
   * 
   * @returns {Object} Safe configuration object
   */
  getSafeConfig() {
    const safeConfig = { ...this.config };
    
    // Remove sensitive keys
    const sensitiveKeys = ['apiKey', 'password', 'token', 'secret'];
    for (const key of Object.keys(safeConfig)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        safeConfig[key] = safeConfig[key] ? '***' : undefined;
      }
    }
    
    return safeConfig;
  }

  /**
   * Update service statistics
   * 
   * @param {Object} updates - Statistics updates
   */
  updateStats(updates) {
    this.stats = { ...this.stats, ...updates };
    this.lastActivity = new Date();
  }
}

module.exports = BaseService;