const BaseService = require('./base-service');
const { createClient } = require('@supabase/supabase-js');

/**
 * Weather Service
 * 
 * Provides weather data synchronization and forecasting for the backend.
 * Fetches weather data from external APIs and stores it in the database.
 */
class WeatherService extends BaseService {
  constructor(options = {}) {
    super('weather', options);
    
    // Service-specific state
    this.weatherClient = null;
    this.supabase = null;
    this.syncTaskId = null;
    this.lastSyncTime = null;
    this.syncStats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastSyncDuration: 0,
      forecastDays: 5
    };
  }

  /**
   * Load configuration from environment variables
   * 
   * @returns {Promise<void>}
   */
  async loadConfiguration() {
    await super.loadConfiguration();
    
    this.config = {
      ...this.config,
      enabled: process.env.WEATHER_ENABLED === 'true',
      apiKey: process.env.WEATHER_API_KEY,
      latitude: parseFloat(process.env.WEATHER_LATITUDE),
      longitude: parseFloat(process.env.WEATHER_LONGITUDE),
      units: process.env.WEATHER_UNITS || 'metric',
      syncInterval: parseInt(process.env.WEATHER_SYNC_INTERVAL || '43200'), // 12 hours in seconds
      provider: process.env.WEATHER_PROVIDER || 'openweathermap',
      forecastDays: parseInt(process.env.WEATHER_FORECAST_DAYS || '5'),
      maxRetries: parseInt(process.env.WEATHER_MAX_RETRIES || '3'),
      retryDelay: parseInt(process.env.WEATHER_RETRY_DELAY || '5000'),
      maxRetainDays: parseInt(process.env.WEATHER_MAX_RETAIN_DAYS || '30')
    };
  }

  /**
   * Validate service configuration
   * 
   * @returns {Promise<void>}
   */
  async validateConfiguration() {
    await super.validateConfiguration();
    
    const validationErrors = [];
    
    if (!this.config.apiKey) {
      validationErrors.push('WEATHER_API_KEY is required');
    }
    
    if (isNaN(this.config.latitude) || isNaN(this.config.longitude)) {
      validationErrors.push('WEATHER_LATITUDE and WEATHER_LONGITUDE must be valid numbers');
    }
    
    if (!['metric', 'imperial'].includes(this.config.units)) {
      validationErrors.push('WEATHER_UNITS must be either "metric" or "imperial"');
    }
    
    if (this.config.syncInterval < 1800) {
      validationErrors.push('WEATHER_SYNC_INTERVAL must be at least 1800 seconds (30 minutes)');
    }
    
    if (this.config.forecastDays < 1 || this.config.forecastDays > 7) {
      validationErrors.push('WEATHER_FORECAST_DAYS must be between 1 and 7');
    }
    
    if (validationErrors.length > 0) {
      throw new Error(`Weather service configuration errors: ${validationErrors.join(', ')}`);
    }
  }

  /**
   * Initialize database connections
   * 
   * @returns {Promise<void>}
   */
  async initializeDatabase() {
    await super.initializeDatabase();
    
    // Initialize Supabase client for database operations
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('SUPABASE_URL and SUPABASE_ANON_KEY are required for Weather service');
    }
    
    this.supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test database connection
    try {
      const { data, error } = await this.supabase
        .from('weather_data')
        .select('count')
        .limit(1);
      
      if (error) {
        throw error;
      }
      
      this.logger.debug('Database connection established successfully');
      
    } catch (error) {
      this.logger.error('Database connection failed', { error: error.message });
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }

  /**
   * Service-specific initialization
   * 
   * @returns {Promise<void>}
   */
  async onInitialize() {
    await super.onInitialize();
    
    // Initialize weather API client
    this.weatherClient = new WeatherAPIClient({
      apiKey: this.config.apiKey,
      provider: this.config.provider,
      maxRetries: this.config.maxRetries,
      retryDelay: this.config.retryDelay,
      logger: this.logger
    });
    
    // Validate API key
    try {
      const isValid = await this.weatherClient.validateApiKey();
      if (!isValid) {
        throw new Error('Invalid weather API key');
      }
      this.logger.info('Weather API key validated successfully');
    } catch (error) {
      this.logger.error('Weather API key validation failed', { error: error.message });
      throw error;
    }
    
    // Load sync statistics and last sync time
    await this.loadSyncStats();
  }

  /**
   * Service-specific start logic
   * 
   * @returns {Promise<void>}
   */
  async onStart() {
    await super.onStart();
    
    // Schedule sync task
    const syncIntervalMs = this.config.syncInterval * 1000;
    this.syncTaskId = this.scheduleTask(
      'weather-sync',
      () => this.performSync(),
      syncIntervalMs
    );
    
    this.logger.info('Weather sync task scheduled', {
      intervalSeconds: this.config.syncInterval,
      nextSync: new Date(Date.now() + syncIntervalMs)
    });
    
    // Schedule cleanup task
    this.scheduleTask(
      'weather-cleanup',
      () => this.cleanupOldData(),
      24 * 60 * 60 * 1000 // Daily cleanup
    );
    
    // Schedule stats update task
    this.scheduleTask(
      'weather-stats',
      () => this.updateSyncStats(),
      5 * 60 * 1000 // Every 5 minutes
    );
    
    // Perform initial sync if needed
    if (this.shouldPerformInitialSync()) {
      this.logger.info('Performing initial weather sync');
      setImmediate(() => this.performSync());
    }
  }

  /**
   * Service-specific stop logic
   * 
   * @returns {Promise<void>}
   */
  async onStop() {
    await super.onStop();
    
    // Save final sync statistics
    await this.saveSyncStats();
    
    this.logger.info('Weather service stopped');
  }

  /**
   * Service-specific cleanup logic
   * 
   * @returns {Promise<void>}
   */
  async onCleanup() {
    await super.onCleanup();
    
    // Close weather client connections
    if (this.weatherClient) {
      await this.weatherClient.close();
      this.weatherClient = null;
    }
  }

  /**
   * Perform weather data synchronization
   * 
   * @returns {Promise<Object>} Sync result
   */
  async performSync() {
    const startTime = Date.now();
    const syncId = `sync-${Date.now()}`;
    
    try {
      this.logger.info('Starting weather sync', { syncId });
      
      this.syncStats.totalSyncs++;
      
      // Fetch current weather
      const currentWeather = await this.weatherClient.getCurrentWeather(
        this.config.latitude,
        this.config.longitude,
        this.config.units
      );
      
      // Fetch forecast
      const forecast = await this.weatherClient.getForecast(
        this.config.latitude,
        this.config.longitude,
        this.config.units,
        this.config.forecastDays
      );
      
      // Store weather data
      await this.storeWeatherData(currentWeather, forecast);
      
      // Update sync statistics
      this.syncStats.successfulSyncs++;
      this.syncStats.lastSyncDuration = Date.now() - startTime;
      this.lastSyncTime = new Date();
      
      // Update last sync time in database
      await this.saveSyncStats();
      
      this.logger.info('Weather sync completed successfully', {
        syncId,
        duration: this.syncStats.lastSyncDuration,
        forecastDays: this.config.forecastDays
      });
      
      // Update service statistics
      this.updateStats({
        operationsCount: this.syncStats.totalSyncs,
        errorsCount: this.syncStats.failedSyncs,
        lastOperationTime: this.lastSyncTime
      });
      
      return {
        success: true,
        duration: this.syncStats.lastSyncDuration,
        message: `Updated weather data and ${this.config.forecastDays}-day forecast`
      };
      
    } catch (error) {
      this.syncStats.failedSyncs++;
      this.logger.error('Weather sync failed', { error: error.message, syncId });
      
      // Update service statistics
      this.updateStats({
        operationsCount: this.syncStats.totalSyncs,
        errorsCount: this.syncStats.failedSyncs
      });
      
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Store weather data in database
   * 
   * @param {Object} currentWeather - Current weather data
   * @param {Array} forecast - Forecast data
   * @returns {Promise<void>}
   */
  async storeWeatherData(currentWeather, forecast) {
    // Store current weather
    const currentData = this.transformCurrentWeatherData(currentWeather);
    const { error: currentError } = await this.supabase
      .from('weather_data')
      .upsert(currentData, {
        onConflict: 'location_key,timestamp'
      });
    
    if (currentError) {
      throw new Error(`Failed to store current weather: ${currentError.message}`);
    }
    
    // Store forecast data
    for (const forecastItem of forecast) {
      const forecastData = this.transformForecastData(forecastItem);
      const { error: forecastError } = await this.supabase
        .from('weather_forecast')
        .upsert(forecastData, {
          onConflict: 'location_key,forecast_date'
        });
      
      if (forecastError) {
        this.logger.error('Failed to store forecast item', { error: forecastError.message });
      }
    }
  }

  /**
   * Transform current weather data for database storage
   * 
   * @param {Object} weatherData - Raw weather data from API
   * @returns {Object} Transformed data for database
   */
  transformCurrentWeatherData(weatherData) {
    return {
      location_key: this.getLocationKey(),
      timestamp: new Date(),
      temperature: weatherData.temperature,
      feels_like: weatherData.feels_like,
      humidity: weatherData.humidity,
      pressure: weatherData.pressure,
      wind_speed: weatherData.wind_speed,
      wind_direction: weatherData.wind_direction,
      visibility: weatherData.visibility,
      uv_index: weatherData.uv_index,
      condition: weatherData.condition,
      condition_code: weatherData.condition_code,
      icon: weatherData.icon,
      sunrise: weatherData.sunrise,
      sunset: weatherData.sunset,
      units: this.config.units,
      raw_data: weatherData,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Transform forecast data for database storage
   * 
   * @param {Object} forecastItem - Raw forecast data from API
   * @returns {Object} Transformed data for database
   */
  transformForecastData(forecastItem) {
    return {
      location_key: this.getLocationKey(),
      forecast_date: forecastItem.date,
      temperature_min: forecastItem.temp_min,
      temperature_max: forecastItem.temp_max,
      humidity: forecastItem.humidity,
      pressure: forecastItem.pressure,
      wind_speed: forecastItem.wind_speed,
      wind_direction: forecastItem.wind_direction,
      condition: forecastItem.condition,
      condition_code: forecastItem.condition_code,
      icon: forecastItem.icon,
      precipitation_probability: forecastItem.precipitation_probability,
      precipitation_amount: forecastItem.precipitation_amount,
      units: this.config.units,
      raw_data: forecastItem,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Get location key for database storage
   * 
   * @returns {string} Location key
   */
  getLocationKey() {
    return `${this.config.latitude.toFixed(4)},${this.config.longitude.toFixed(4)}`;
  }

  /**
   * Load sync statistics from database
   * 
   * @returns {Promise<void>}
   */
  async loadSyncStats() {
    try {
      const { data, error } = await this.supabase
        .from('service_metadata')
        .select('metadata')
        .eq('service_name', 'weather')
        .eq('key', 'sync_stats')
        .single();
      
      if (error && error.code !== 'PGRST116') { // Not found error
        throw error;
      }
      
      if (data && data.metadata) {
        this.syncStats = { ...this.syncStats, ...data.metadata };
        this.lastSyncTime = data.metadata.lastSyncTime ? new Date(data.metadata.lastSyncTime) : null;
      }
      
    } catch (error) {
      this.logger.warn('Failed to load sync statistics', { error: error.message });
    }
  }

  /**
   * Save sync statistics to database
   * 
   * @returns {Promise<void>}
   */
  async saveSyncStats() {
    try {
      const statsData = {
        ...this.syncStats,
        lastSyncTime: this.lastSyncTime ? this.lastSyncTime.toISOString() : null
      };
      
      const { error } = await this.supabase
        .from('service_metadata')
        .upsert([{
          service_name: 'weather',
          key: 'sync_stats',
          metadata: statsData,
          updated_at: new Date().toISOString()
        }]);
      
      if (error) {
        throw error;
      }
      
    } catch (error) {
      this.logger.warn('Failed to save sync statistics', { error: error.message });
    }
  }

  /**
   * Update sync statistics
   * 
   * @returns {Promise<void>}
   */
  async updateSyncStats() {
    await this.saveSyncStats();
  }

  /**
   * Check if initial sync should be performed
   * 
   * @returns {boolean} Whether to perform initial sync
   */
  shouldPerformInitialSync() {
    if (!this.lastSyncTime) {
      return true;
    }
    
    const timeSinceLastSync = Date.now() - this.lastSyncTime.getTime();
    const syncIntervalMs = this.config.syncInterval * 1000;
    
    return timeSinceLastSync >= syncIntervalMs;
  }

  /**
   * Clean up old weather data
   * 
   * @returns {Promise<void>}
   */
  async cleanupOldData() {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.maxRetainDays);
      
      // Clean up old weather data
      const { data: weatherData, error: weatherError } = await this.supabase
        .from('weather_data')
        .delete()
        .lt('created_at', cutoffDate.toISOString());
      
      if (weatherError) {
        throw weatherError;
      }
      
      // Clean up old forecast data
      const { data: forecastData, error: forecastError } = await this.supabase
        .from('weather_forecast')
        .delete()
        .lt('created_at', cutoffDate.toISOString());
      
      if (forecastError) {
        throw forecastError;
      }
      
      const weatherDeleted = weatherData ? weatherData.length : 0;
      const forecastDeleted = forecastData ? forecastData.length : 0;
      
      this.logger.info('Cleaned up old weather data', {
        weatherDeleted,
        forecastDeleted,
        cutoffDate: cutoffDate.toISOString(),
        maxRetainDays: this.config.maxRetainDays
      });
      
    } catch (error) {
      this.logger.error('Failed to cleanup old weather data', { error: error.message });
    }
  }

  /**
   * Get current weather data
   * 
   * @returns {Promise<Object>} Current weather data
   */
  async getCurrentWeather() {
    try {
      const { data, error } = await this.supabase
        .from('weather_data')
        .select('*')
        .eq('location_key', this.getLocationKey())
        .order('timestamp', { ascending: false })
        .limit(1);
      
      if (error) {
        throw error;
      }
      
      return data && data.length > 0 ? data[0] : null;
    } catch (error) {
      this.logger.error('Failed to get current weather', { error: error.message });
      throw error;
    }
  }

  /**
   * Get weather forecast
   * 
   * @param {number} days - Number of forecast days
   * @returns {Promise<Array>} Forecast data
   */
  async getForecast(days = 5) {
    try {
      const { data, error } = await this.supabase
        .from('weather_forecast')
        .select('*')
        .eq('location_key', this.getLocationKey())
        .gte('forecast_date', new Date().toISOString().split('T')[0])
        .order('forecast_date', { ascending: true })
        .limit(days);
      
      if (error) {
        throw error;
      }
      
      return data || [];
    } catch (error) {
      this.logger.error('Failed to get forecast', { error: error.message });
      throw error;
    }
  }

  /**
   * Perform manual sync
   * 
   * @returns {Promise<Object>} Sync result
   */
  async performManualSync() {
    this.logger.info('Manual weather sync requested');
    return await this.performSync();
  }

  /**
   * Get service-specific status
   * 
   * @returns {Object} Enhanced status information
   */
  getStatus() {
    const baseStatus = super.getStatus();
    
    return {
      ...baseStatus,
      lastSyncTime: this.lastSyncTime,
      nextSyncTime: this.syncTaskId ? 
        new Date(Date.now() + (this.config.syncInterval * 1000)) : null,
      syncStats: { ...this.syncStats },
      location: {
        latitude: this.config.latitude,
        longitude: this.config.longitude,
        locationKey: this.getLocationKey()
      },
      provider: this.config.provider,
      forecastDays: this.config.forecastDays
    };
  }
}

/**
 * Weather API Client
 * 
 * Handles communication with weather API providers
 */
class WeatherAPIClient {
  constructor(options = {}) {
    this.apiKey = options.apiKey;
    this.provider = options.provider || 'openweathermap';
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 5000;
    this.logger = options.logger;
    
    // API endpoints for different providers
    this.endpoints = {
      openweathermap: {
        baseUrl: 'https://api.openweathermap.org/data/2.5',
        current: '/weather',
        forecast: '/forecast'
      }
    };
  }

  /**
   * Validate API key
   * 
   * @returns {Promise<boolean>} Whether API key is valid
   */
  async validateApiKey() {
    try {
      // Test with a simple request
      await this.getCurrentWeather(0, 0, 'metric');
      return true;
    } catch (error) {
      if (this.logger) {
        this.logger.error('Weather API key validation failed', { error: error.message });
      }
      return false;
    }
  }

  /**
   * Get current weather
   * 
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @param {string} units - Units (metric/imperial)
   * @returns {Promise<Object>} Current weather data
   */
  async getCurrentWeather(lat, lon, units = 'metric') {
    const endpoint = this.endpoints[this.provider];
    const url = `${endpoint.baseUrl}${endpoint.current}`;
    
    const params = {
      lat: lat.toString(),
      lon: lon.toString(),
      units,
      appid: this.apiKey
    };
    
    const response = await this.makeRequest(url, { params });
    return this.transformCurrentWeatherResponse(response);
  }

  /**
   * Get weather forecast
   * 
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @param {string} units - Units (metric/imperial)
   * @param {number} days - Number of forecast days
   * @returns {Promise<Array>} Forecast data
   */
  async getForecast(lat, lon, units = 'metric', days = 5) {
    const endpoint = this.endpoints[this.provider];
    const url = `${endpoint.baseUrl}${endpoint.forecast}`;
    
    const params = {
      lat: lat.toString(),
      lon: lon.toString(),
      units,
      appid: this.apiKey,
      cnt: (days * 8).toString() // 8 forecasts per day (3-hour intervals)
    };
    
    const response = await this.makeRequest(url, { params });
    return this.transformForecastResponse(response, days);
  }

  /**
   * Transform current weather API response
   * 
   * @param {Object} response - API response
   * @returns {Object} Transformed weather data
   */
  transformCurrentWeatherResponse(response) {
    return {
      temperature: response.main.temp,
      feels_like: response.main.feels_like,
      humidity: response.main.humidity,
      pressure: response.main.pressure,
      wind_speed: response.wind?.speed || 0,
      wind_direction: response.wind?.deg || 0,
      visibility: response.visibility || 0,
      uv_index: response.uvi || 0,
      condition: response.weather[0]?.description || '',
      condition_code: response.weather[0]?.id || 0,
      icon: response.weather[0]?.icon || '',
      sunrise: response.sys?.sunrise ? new Date(response.sys.sunrise * 1000) : null,
      sunset: response.sys?.sunset ? new Date(response.sys.sunset * 1000) : null
    };
  }

  /**
   * Transform forecast API response
   * 
   * @param {Object} response - API response
   * @param {number} days - Number of forecast days
   * @returns {Array} Transformed forecast data
   */
  transformForecastResponse(response, days) {
    const forecasts = [];
    const dailyData = {};
    
    // Group forecasts by date
    for (const item of response.list) {
      const date = new Date(item.dt * 1000).toISOString().split('T')[0];
      
      if (!dailyData[date]) {
        dailyData[date] = {
          date,
          temps: [],
          conditions: [],
          humidity: [],
          pressure: [],
          wind_speed: [],
          wind_direction: [],
          precipitation: []
        };
      }
      
      dailyData[date].temps.push(item.main.temp);
      dailyData[date].conditions.push(item.weather[0]);
      dailyData[date].humidity.push(item.main.humidity);
      dailyData[date].pressure.push(item.main.pressure);
      dailyData[date].wind_speed.push(item.wind?.speed || 0);
      dailyData[date].wind_direction.push(item.wind?.deg || 0);
      dailyData[date].precipitation.push(item.pop || 0);
    }
    
    // Create daily forecasts
    for (const [date, data] of Object.entries(dailyData)) {
      if (forecasts.length >= days) break;
      
      const condition = data.conditions[0] || {};
      
      forecasts.push({
        date,
        temp_min: Math.min(...data.temps),
        temp_max: Math.max(...data.temps),
        humidity: Math.round(data.humidity.reduce((a, b) => a + b) / data.humidity.length),
        pressure: Math.round(data.pressure.reduce((a, b) => a + b) / data.pressure.length),
        wind_speed: Math.round(data.wind_speed.reduce((a, b) => a + b) / data.wind_speed.length),
        wind_direction: Math.round(data.wind_direction.reduce((a, b) => a + b) / data.wind_direction.length),
        condition: condition.description || '',
        condition_code: condition.id || 0,
        icon: condition.icon || '',
        precipitation_probability: Math.max(...data.precipitation) * 100,
        precipitation_amount: 0 // Would need additional API call for this
      });
    }
    
    return forecasts;
  }

  /**
   * Make HTTP request to weather API
   * 
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async makeRequest(url, options = {}) {
    const { params = {} } = options;
    
    const searchParams = new URLSearchParams(params);
    const fullUrl = `${url}?${searchParams.toString()}`;
    
    let lastError;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(fullUrl, {
          method: 'GET',
          headers: {
            'User-Agent': 'Lifeboard-Backend/1.0'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
        
      } catch (error) {
        lastError = error;
        
        if (this.logger) {
          this.logger.warn(`Weather API request failed (attempt ${attempt}/${this.maxRetries})`, { 
            error: error.message 
          });
        }
        
        if (attempt < this.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Close client connections
   * 
   * @returns {Promise<void>}
   */
  async close() {
    // Clean up any persistent connections if needed
  }
}

module.exports = WeatherService;