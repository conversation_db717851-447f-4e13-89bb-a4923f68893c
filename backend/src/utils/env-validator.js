const joi = require('joi');

/**
 * Environment variable validation
 */
const envSchema = joi.object({
  // Server configuration
  NODE_ENV: joi.string().valid('development', 'production', 'test').default('development'),
  PORT: joi.number().port().default(3000),
  HOST: joi.string().default('0.0.0.0'),
  LOG_LEVEL: joi.string().valid('error', 'warn', 'info', 'debug', 'verbose').default('info'),
  API_PREFIX: joi.string().default('/api/v1'),
  
  // CORS configuration
  CORS_ORIGIN: joi.string().default('*'),
  
  // Database configuration
  SUPABASE_URL: joi.string().uri().required(),
  SUPABASE_SERVICE_ROLE_KEY: joi.string().required(),
  
  // Service configuration
  LIMITLESS_ENABLED: joi.string().valid('true', 'false').default('false'),
  LIMITLESS_API_KEY: joi.string().when('LIMITLESS_ENABLED', {
    is: 'true',
    then: joi.required(),
    otherwise: joi.optional()
  }),
  LIMITLESS_BASE_URL: joi.string().uri().default('https://api.limitless.ai'),
  LIMITLESS_SYNC_INTERVAL: joi.number().min(3600).default(14400), // 4 hours
  LIMITLESS_BATCH_SIZE: joi.number().min(1).max(1000).default(100),
  LIMITLESS_MAX_RETRIES: joi.number().min(1).max(10).default(3),
  LIMITLESS_RETRY_DELAY: joi.number().min(1000).default(5000),
  
  // Weather service configuration
  WEATHER_ENABLED: joi.string().valid('true', 'false').default('false'),
  WEATHER_API_KEY: joi.string().when('WEATHER_ENABLED', {
    is: 'true',
    then: joi.required(),
    otherwise: joi.optional()
  }),
  WEATHER_LATITUDE: joi.number().min(-90).max(90).when('WEATHER_ENABLED', {
    is: 'true',
    then: joi.required(),
    otherwise: joi.optional()
  }),
  WEATHER_LONGITUDE: joi.number().min(-180).max(180).when('WEATHER_ENABLED', {
    is: 'true',
    then: joi.required(),
    otherwise: joi.optional()
  }),
  WEATHER_UNITS: joi.string().valid('metric', 'imperial').default('metric'),
  WEATHER_SYNC_INTERVAL: joi.number().min(1800).default(43200), // 12 hours
  WEATHER_PROVIDER: joi.string().valid('openweathermap').default('openweathermap'),
  WEATHER_FORECAST_DAYS: joi.number().min(1).max(16).default(5),
  WEATHER_MAX_RETRIES: joi.number().min(1).max(10).default(3),
  WEATHER_RETRY_DELAY: joi.number().min(1000).default(5000)
}).unknown(true); // Allow other environment variables

/**
 * Validate environment variables
 * 
 * @returns {Promise<Object>} Validated environment variables
 * @throws {Error} If validation fails
 */
async function validateEnvironment() {
  try {
    const { error, value } = envSchema.validate(process.env, {
      abortEarly: false,
      stripUnknown: false
    });

    if (error) {
      const errorMessages = error.details.map(detail => detail.message);
      throw new Error(`Environment validation failed:\n${errorMessages.join('\n')}`);
    }

    // Update process.env with validated values
    Object.assign(process.env, value);

    return value;
  } catch (error) {
    console.error('Environment validation error:', error.message);
    throw error;
  }
}

/**
 * Get required environment variables for a service
 * 
 * @param {string} serviceName - Name of the service
 * @returns {Array} Array of required environment variables
 */
function getRequiredEnvForService(serviceName) {
  const serviceEnvMap = {
    limitless: [
      'LIMITLESS_ENABLED',
      'LIMITLESS_API_KEY',
      'LIMITLESS_BASE_URL',
      'SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY'
    ],
    weather: [
      'WEATHER_ENABLED',
      'WEATHER_API_KEY',
      'WEATHER_LATITUDE',
      'WEATHER_LONGITUDE',
      'SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY'
    ]
  };

  return serviceEnvMap[serviceName] || [];
}

/**
 * Check if a service is properly configured
 * 
 * @param {string} serviceName - Name of the service
 * @returns {boolean} Whether the service is properly configured
 */
function isServiceConfigured(serviceName) {
  const requiredEnv = getRequiredEnvForService(serviceName);
  
  for (const envVar of requiredEnv) {
    if (!process.env[envVar]) {
      return false;
    }
  }
  
  // Check if service is enabled
  const enabledVar = `${serviceName.toUpperCase()}_ENABLED`;
  return process.env[enabledVar] === 'true';
}

module.exports = {
  validateEnvironment,
  getRequiredEnvForService,
  isServiceConfigured
};