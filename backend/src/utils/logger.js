const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

/**
 * Logger utility for backend services
 */
class Logger {
  constructor(options = {}) {
    this.component = options.component || 'backend';
    this.level = options.level || 'info';
    this.logDir = options.logDir || path.join(process.cwd(), 'logs');
    
    this.logger = this.createLogger();
  }

  createLogger() {
    const logFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    );

    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.printf(({ timestamp, level, message, component, ...meta }) => {
        const componentStr = component ? `[${component}]` : '';
        const metaStr = Object.keys(meta).length ? JSON.stringify(meta) : '';
        return `${timestamp} ${level} ${componentStr} ${message} ${metaStr}`;
      })
    );

    const transports = [
      new winston.transports.Console({
        level: this.level,
        format: consoleFormat
      }),
      new DailyRotateFile({
        filename: path.join(this.logDir, `${this.component}-%DATE%.log`),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '14d',
        level: this.level,
        format: logFormat
      }),
      new DailyRotateFile({
        filename: path.join(this.logDir, `${this.component}-error-%DATE%.log`),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        level: 'error',
        format: logFormat
      })
    ];

    return winston.createLogger({
      level: this.level,
      format: logFormat,
      defaultMeta: { component: this.component },
      transports
    });
  }

  info(message, meta = {}) {
    this.logger.info(message, meta);
  }

  error(message, meta = {}) {
    this.logger.error(message, meta);
  }

  warn(message, meta = {}) {
    this.logger.warn(message, meta);
  }

  debug(message, meta = {}) {
    this.logger.debug(message, meta);
  }

  verbose(message, meta = {}) {
    this.logger.verbose(message, meta);
  }
}

module.exports = { Logger };