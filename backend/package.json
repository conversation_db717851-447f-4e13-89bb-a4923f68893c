{"name": "lifeboard-backend", "version": "1.0.0", "description": "Lifeboard Backend Service Architecture", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.3.1", "node-cron": "^3.0.3", "axios": "^1.6.2", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["backend", "services", "api", "lifeboard"], "author": "Lifeboard Team", "license": "MIT"}