# Backend Environment Configuration
# Copy this file to .env and update the values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3000
HOST=localhost

# =============================================================================
# DATABASE CONFIGURATION (Supabase)
# =============================================================================
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_DIR=../logs
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# =============================================================================
# WEATHER SERVICE CONFIGURATION
# =============================================================================
WEATHER_ENABLED=true
WEATHER_API_KEY=your-openweathermap-api-key
WEATHER_LATITUDE=40.7128
WEATHER_LONGITUDE=-74.0060
WEATHER_UNITS=metric
WEATHER_SYNC_INTERVAL=43200
WEATHER_PROVIDER=openweathermap
WEATHER_FORECAST_DAYS=5
WEATHER_MAX_RETRIES=3
WEATHER_RETRY_DELAY=5000
WEATHER_MAX_RETAIN_DAYS=30

# =============================================================================
# LIMITLESS SERVICE CONFIGURATION
# =============================================================================
LIMITLESS_ENABLED=true
LIMITLESS_API_KEY=your-limitless-api-key
LIMITLESS_API_URL=https://api.limitless.ai
LIMITLESS_CAPTURE_INTERVAL=60000
LIMITLESS_BATCH_SIZE=10
LIMITLESS_RETRY_ATTEMPTS=3
LIMITLESS_RETRY_DELAY=5000
LIMITLESS_MAX_RETAIN_DAYS=90

# Capture Types
LIMITLESS_CAPTURE_WEB_BROWSING=true
LIMITLESS_CAPTURE_APPLICATIONS=true
LIMITLESS_CAPTURE_SYSTEM_EVENTS=true
LIMITLESS_CAPTURE_SCREENSHOTS=false

# Privacy Settings
LIMITLESS_PRIVACY_MODE=true
LIMITLESS_EXCLUDE_URLS=localhost,127.0.0.1,private.com
LIMITLESS_EXCLUDE_APPS=password,keychain,wallet

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000