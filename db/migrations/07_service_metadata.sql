-- Service Metadata Table
-- Stores service-specific metadata, configuration, and statistics

-- Create service_metadata table
CREATE TABLE IF NOT EXISTS service_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_name TEXT NOT NULL,
    key TEXT NOT NULL,
    metadata JSONB,
    value TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(service_name, key)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_service_metadata_service_name ON service_metadata(service_name);
CREATE INDEX IF NOT EXISTS idx_service_metadata_key ON service_metadata(key);
CREATE INDEX IF NOT EXISTS idx_service_metadata_service_key ON service_metadata(service_name, key);
CREATE INDEX IF NOT EXISTS idx_service_metadata_updated_at ON service_metadata(updated_at);

-- Add GIN index for JSONB metadata column for efficient queries
CREATE INDEX IF NOT EXISTS idx_service_metadata_metadata ON service_metadata USING GIN(metadata);

-- Enable Row Level Security
ALTER TABLE service_metadata ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "service_metadata_select_policy" ON service_metadata
    FOR SELECT USING (true);

CREATE POLICY "service_metadata_insert_policy" ON service_metadata
    FOR INSERT WITH CHECK (true);

CREATE POLICY "service_metadata_update_policy" ON service_metadata
    FOR UPDATE USING (true);

CREATE POLICY "service_metadata_delete_policy" ON service_metadata
    FOR DELETE USING (true);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_service_metadata_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_service_metadata_updated_at
    BEFORE UPDATE ON service_metadata
    FOR EACH ROW
    EXECUTE FUNCTION update_service_metadata_updated_at();

-- Insert some initial service metadata entries
INSERT INTO service_metadata (service_name, key, metadata, value) VALUES
    ('system', 'migration_version', '{"version": "07", "description": "Service metadata table"}', '07'),
    ('system', 'created_at', '{"timestamp": "' || NOW() || '"}', NOW()::text)
ON CONFLICT (service_name, key) DO NOTHING;

-- Add helpful comments
COMMENT ON TABLE service_metadata IS 'Stores service-specific metadata, configuration, and statistics';
COMMENT ON COLUMN service_metadata.service_name IS 'Name of the service (e.g., weather, limitless)';
COMMENT ON COLUMN service_metadata.key IS 'Metadata key (e.g., sync_stats, capture_stats, last_sync_time)';
COMMENT ON COLUMN service_metadata.metadata IS 'JSON metadata object for complex data';
COMMENT ON COLUMN service_metadata.value IS 'Simple text value for basic data';
COMMENT ON COLUMN service_metadata.created_at IS 'When the metadata entry was created';
COMMENT ON COLUMN service_metadata.updated_at IS 'When the metadata entry was last updated';