-- Limitless Lifelogs Table
-- Migration for storing Limitless AI lifelog data

-- Create the limitless_lifelogs table
CREATE TABLE limitless_lifelogs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    lifelog_id TEXT NOT NULL,
    title TEXT,
    markdown TEXT,
    contents JSONB,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Add unique constraint to prevent duplicate entries
ALTER TABLE limitless_lifelogs 
ADD CONSTRAINT unique_user_lifelog UNIQUE (user_id, lifelog_id);

-- Create indexes for performance
CREATE INDEX idx_limitless_lifelogs_user_id ON limitless_lifelogs(user_id);
CREATE INDEX idx_limitless_lifelogs_start_time ON limitless_lifelogs(start_time DESC);
CREATE INDEX idx_limitless_lifelogs_user_start_time ON limitless_lifelogs(user_id, start_time DESC);
CREATE INDEX idx_limitless_lifelogs_lifelog_id ON limitless_lifelogs(lifelog_id);